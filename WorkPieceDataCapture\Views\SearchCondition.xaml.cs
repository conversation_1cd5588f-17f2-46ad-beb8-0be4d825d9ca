﻿using Models;
using System.ComponentModel;
using System.Reflection;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using UserControls.CustControls;

namespace WorkPieceDataCapture.Views
{
    /// <summary>
    /// SearchCondition.xaml 的交互逻辑
    /// </summary>
    public partial class SearchCondition : Window
    {
        public model_SearchCondition? conditions { get; set; }
        public SearchCondition()
        {
            InitializeComponent();
            this.conditions = new model_SearchCondition();
            this.conditions.dDate_End = new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day, 23, 59, 59);
            this.conditions.dDate_Begin = this.conditions.dDate_End.Value.AddDays(-7);
            this.DataContext = conditions;
        }

        private void Window_Loaded(object sender, RoutedEventArgs e)
        {
        }

        private void event_DateTimePicker(object sender, MouseButtonEventArgs e)
        {
            var txt = sender as TextBox;
            if (txt != null)
            {
                DateTimePicker dialog = new DateTimePicker();
                dialog.Owner = this;
                dialog.ShowDialog();
                if (dialog.DialogResult == true)
                {
                    txt.Text = dialog.strSelectedDateTime;
                    switch (txt.Name)
                    {
                        case "txtDate_Begin":
                            this.conditions.dDate_Begin = DateTime.Parse(txt.Text);
                            break;
                        case "txtDate_End":
                            this.conditions.dDate_End = DateTime.Parse(txt.Text);
                            break;
                    }
                }
            }
        }

        private void Button_Click(object sender, RoutedEventArgs e)
        {
            Button btn = (Button)sender;
            switch (btn.Content.ToString())
            {
                case "查询":
                    this.DialogResult = true;
                    this.Close();
                    break;
                case "取消":
                    this.DialogResult = false;
                    this.Close();
                    break;
                case "重置":
                    Type type = this.conditions.GetType();
                    PropertyInfo[] properties = type.GetProperties();
                    foreach (PropertyInfo property in properties)
                    {
                        if (property.Name.ToLower().Contains("_result"))
                        {
                            property.SetValue(this.conditions, "全部");
                        }
                        else
                        {
                            property.SetValue(this.conditions, null);
                        }
                    }
                    break;
                default:
                    break;
            }
        }
        public event PropertyChangedEventHandler? PropertyChanged;
        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
