﻿using Interfaces.Model;

namespace Models
{
    /// <summary>
    /// 生产统计实体类
    /// </summary>
    public class ProduceSummary
    {
        public int production24Hour { get; set; }   //24小时产量
        public int OK24Hour { get; set; }   //24小时OK数
        public int NG24Hour { get; set; }   //24小时NG数
        public string OKRate24Hour { get; set; }    //24小时良品率
        public int productionToday { get; set; }   //今日产量
        public int OKToday { get; set; }   //今日OK数
        public int NGToday { get; set; }   //今日NG数
        public string OKRateToday { get; set; }    //今日良品率
    }
}
