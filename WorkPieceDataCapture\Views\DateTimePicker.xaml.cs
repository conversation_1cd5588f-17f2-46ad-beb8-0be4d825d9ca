﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace UserControls.CustControls
{
    /// <summary>
    /// DateTimePicker.xaml 的交互逻辑
    /// </summary>
    public partial class DateTimePicker : Window
    {
        public string strSelectedDateTime { get; set; }
        public DateTimePicker()
        {
            InitializeComponent();
        }

        private void Window_Loaded(object sender, RoutedEventArgs e)
        {
            this.cal.SelectedDate = DateTime.Now;
            this.cbHour.Text = DateTime.Now.Hour.ToString().PadLeft(2, '0');
            this.cbMinute.Text = DateTime.Now.Minute.ToString().PadLeft(2, '0');
        }

        private void btnCancel_Click(object sender, RoutedEventArgs e)
        {
            this.DialogResult = false;
            this.Close();
        }

        private void btnConfirm_Click(object sender, RoutedEventArgs e)
        {
            if (this.cal.SelectedDate != null)
            {
                this.strSelectedDateTime = DateTime.Parse(this.cal.SelectedDate.ToString()).ToString("yyyy-MM-dd") + " " + cbHour.Text + ":" + cbMinute.Text;
                this.DialogResult = true;
                this.Close();
            }
        }
    }
}
