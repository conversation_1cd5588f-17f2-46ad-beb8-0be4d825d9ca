﻿﻿using Models;
using Newtonsoft.Json;
using System.Windows;

namespace WorkPieceDataCapture.Views
{
    /// <summary>
    /// CoordinateDataTestWindow.xaml 的交互逻辑
    /// </summary>
    public partial class CoordinateDataTestWindow : Window
    {
        public CoordinateDataTestWindow()
        {
            InitializeComponent();
        }

        private void ShowChart_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 创建一个临时的数据实体
                var data = new tb_WorkPieceDataCapture_Realtime
                {
                    ZC_SerialNo = "TEST-" + DateTime.Now.ToString("yyyyMMddHHmmss"),
                    CoordinateData = txtCoordinateData.Text.Trim()
                };

                // 验证JSON格式
                var testParse = JsonConvert.DeserializeObject<List<CoordinatePoint>>(data.CoordinateData);
                if (testParse == null || testParse.Count == 0)
                {
                    MessageBox.Show("无效的坐标数据格式或数据为空", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                // 打开图表窗口
                var chartWindow = new CoordinateChartWindow(data);
                chartWindow.Owner = this;
                chartWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"处理坐标数据时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
