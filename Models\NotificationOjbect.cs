﻿using System.ComponentModel;

namespace Models
{
    /// <summary>
    /// mvvm的基类
    /// </summary>
    public class NotificationOjbect : INotifyPropertyChanged
    {
        public event PropertyChangedEventHandler PropertyChanged;

        public void RaisePropertyChanged(string PropertyName)
        {
            if (this.PropertyChanged != null)
            {
                this.PropertyChanged.Invoke(this, new PropertyChangedEventArgs(PropertyName));
            }
        }
    }
}
