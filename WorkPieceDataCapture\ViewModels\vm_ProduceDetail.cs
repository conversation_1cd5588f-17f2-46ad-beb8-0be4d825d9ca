﻿using Interfaces.Model;
using Models;
using System.ComponentModel;
using WorkPieceDataCapture.ViewModels;

namespace ViewModels
{
    public class vm_ProduceDetail : vm_Base
    {
        #region 全局变量与枚举
        tb_WorkPieceDataCapture_Realtime _dataModel_Search;
        public tb_WorkPieceDataCapture_Realtime dataModel_Search
        {
            get
            {
                return _dataModel_Search;
            }
            set
            {
                _dataModel_Search = value;
                OnPropertyChanged(nameof(dataModel_Search));
            }
        }
        #endregion
        #region 构造与析构函数
        /// <summary>
        /// 构造函数
        /// </summary>
        public vm_ProduceDetail(tb_WorkPieceDataCapture_Realtime model)
        {
            initialize(model);
        }
        /// <summary>
        /// 析构函数
        /// </summary>
        ~vm_ProduceDetail()
        {

        }
        #endregion
        #region 事件

        /// <summary>
        /// 类的初始化
        /// </summary>
        /// <returns></returns>
        public IFuncResult<string> initialize(tb_WorkPieceDataCapture_Realtime model)
        {
            var result = new FuncResult<string>();
            try
            {
                dataModel_Search = model;
                result.bSuccess = true;
            }
            catch (Exception ex)
            {
                result.setException(ex);
            }
            return result;
        }
        #endregion
        #region 方法

        #endregion

    }
}
