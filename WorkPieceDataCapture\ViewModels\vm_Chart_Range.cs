﻿using Interfaces.Model;
using LiveCharts;
using LiveCharts.Defaults;
using LiveCharts.Wpf;
using Microsoft.Extensions.DependencyInjection;
using Models;
using Newtonsoft.Json;
using Services;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using Utils;
using WorkPieceDataCapture.ViewModels;

namespace ViewModels
{
    public class vm_Chart_Range : vm_Base
    {
        #region 全局变量与枚举
        private IServiceProvider _serviceProvider;
        WorkPieceDataCaptureRespository respository;
        private SeriesCollection _SeriesData;
        public SeriesCollection SeriesData  //饼图的数据源
        {
            get => _SeriesData;
            set
            {
                _SeriesData = value;
                OnPropertyChanged(nameof(SeriesData));
            }
        }
        private model_SearchCondition _conditions;
        public model_SearchCondition conditions
        {
            get
            {
                return _conditions;
            }
            set
            {
                _conditions = value;
                OnPropertyChanged(nameof(conditions));
            }
        }//查询条件
        private string _Yield;
        public string Yield    //统计产量
        {
            get
            {
                return _Yield;

            }
            set
            {
                _Yield = value;
                OnPropertyChanged(nameof(Yield));
            }
        }
        private List<string> _Labels;
        public List<string> Labels
        {
            get
            {
                return _Labels;
            }
            set
            {
                _Labels = value;
                OnPropertyChanged(nameof(Labels));
            }
        }//X轴信息提示
        #endregion
        #region 事件注册
        public ICommand doSummaryCommand { get; set; }   //执行查询事件
        public ICommand doExportCommand { get; set; }  //执行导出明细数据的事件
        #endregion
        #region 构造与析构函数
        /// <summary>
        /// 构造函数
        /// </summary>
        public vm_Chart_Range(IServiceProvider serviceProvider)
        {
            this._serviceProvider = serviceProvider;
            this.respository = new WorkPieceDataCaptureRespository(this._serviceProvider);
            doSummaryCommand = new RelayCommand(doSummary);   //点击查询按钮的事件注册
            doExportCommand = new RelayCommand(doExport);   //执行导出列表的事件注册
            initialize();
        }
        /// <summary>
        /// 析构函数
        /// </summary>
        ~vm_Chart_Range()
        {

        }
        #endregion
        #region 事件
        /// <summary>
        /// 类的初始化
        /// </summary>
        /// <returns></returns>
        public IFuncResult<string> initialize()
        {
            var result = new FuncResult<string>();
            try
            {
                this.conditions = new model_SearchCondition();
                this.conditions.dDate_End = new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day, 23, 59, 59);
                this.conditions.dDate_Begin = this.conditions.dDate_End.Value.AddDays(-7);
                result.bSuccess = true;
            }
            catch (Exception ex)
            {
                result.setException(ex);
            }
            return result;
        }
        /// <summary>
        /// 执行数据查询
        /// </summary>
        /// <param name="parameter"></param>
        private void doSummary(object parameter)
        {
            try
            {
                var context = this._serviceProvider.GetService<AppDbContext>();
                if (context != null)
                {
                    if (conditions.dDate_Begin > conditions.dDate_End)
                    {
                        MessageBox.Show("开始日期不能大于结束日期！");
                        return;
                    }
                    var list_Final = new List<tb_WorkPieceDataCapture_Realtime>();    //存放最终结果
                    var list_FinalJson = new List<string>();    //存放不同实体类但是相同结构的数据的json
                    #region 如果查询的年月区间包含一个月以内的数据，则需要从实时表里查询数据
                    var nowYearMonth = DateTime.Now.ToString("yyyyMM");
                    if (int.Parse(nowYearMonth) >= int.Parse(conditions.dDate_Begin.Value.ToString("yyyyMM"))
                        && (int.Parse(nowYearMonth) <= int.Parse(conditions.dDate_End.Value.ToString("yyyyMM"))))
                    {
                        var result = this.respository.SearchDataFromRealTimeTable(conditions, context.tb_workpiecedatacapture_realtime);
                        if (result.bSuccess)
                        {
                            if (result.obj_Return.Count > 0)
                            {
                                list_Final.AddRange(result.obj_Return);
                            }
                        }
                        else
                        {
                            MessageBox.Show("从实时数据表中获取数据时出现错误！" + result.strMsg);
                            return;
                        }
                    }
                    #endregion
                    #region 根据起始日期到结束日期的年份确认用到的历史表并执行查询
                    for (var i = conditions.dDate_Begin.Value.Year; i <= conditions.dDate_End.Value.Year; i++)
                    {
                        FuncResult<List<tb_WorkPieceDataCapture_Realtime>> result;
                        switch (i)
                        {
                            case 2024:
                                var result2024 = respository.SearchDataFrom2024YearHistoryTable(conditions, context.tb_workpiecedatacapture_history_2024);
                                if (result2024.bSuccess)
                                {
                                    result2024.obj_Return.ForEach(ee =>
                                    {
                                        list_FinalJson.Add(JsonConvert.SerializeObject(ee));
                                    });
                                }
                                else
                                {
                                    MessageBox.Show("从2024年历史表中获取数据时出现错误！" + result2024.strMsg);
                                    return;
                                }
                                break;
                            case 2025:
                                var result2025 = respository.SearchDataFrom2025YearHistoryTable(conditions, context.tb_workpiecedatacapture_history_2025);
                                if (result2025.bSuccess)
                                {
                                    result2025.obj_Return.ForEach(ee =>
                                    {
                                        list_FinalJson.Add(JsonConvert.SerializeObject(ee));
                                    });
                                }
                                else
                                {
                                    MessageBox.Show("从2025年历史表中获取数据时出现错误！" + result2025.strMsg);
                                    return;
                                }
                                break;
                            case 2026:
                                var result2026 = respository.SearchDataFrom2026YearHistoryTable(conditions, context.tb_workpiecedatacapture_history_2026);
                                if (result2026.bSuccess)
                                {
                                    result2026.obj_Return.ForEach(ee =>
                                    {
                                        list_FinalJson.Add(JsonConvert.SerializeObject(ee));
                                    });
                                }
                                else
                                {
                                    MessageBox.Show("从2026年历史表中获取数据时出现错误！" + result2026.strMsg);
                                    return;
                                }
                                break;
                            case 2027:
                                var result2027 = respository.SearchDataFrom2027YearHistoryTable(conditions, context.tb_workpiecedatacapture_history_2027);
                                if (result2027.bSuccess)
                                {
                                    result2027.obj_Return.ForEach(ee =>
                                    {
                                        list_FinalJson.Add(JsonConvert.SerializeObject(ee));
                                    });
                                }
                                else
                                {
                                    MessageBox.Show("从2027年历史表中获取数据时出现错误！" + result2027.strMsg);
                                    return;
                                }
                                break;
                            case 2028:
                                var result2028 = respository.SearchDataFrom2028YearHistoryTable(conditions, context.tb_workpiecedatacapture_history_2028);
                                if (result2028.bSuccess)
                                {
                                    result2028.obj_Return.ForEach(ee =>
                                    {
                                        list_FinalJson.Add(JsonConvert.SerializeObject(ee));
                                    });
                                }
                                else
                                {
                                    MessageBox.Show("从2028年历史表中获取数据时出现错误！" + result2028.strMsg);
                                    return;
                                }
                                break;
                            case 2029:
                                var result2029 = respository.SearchDataFrom2029YearHistoryTable(conditions, context.tb_workpiecedatacapture_history_2029);
                                if (result2029.bSuccess)
                                {
                                    result2029.obj_Return.ForEach(ee =>
                                    {
                                        list_FinalJson.Add(JsonConvert.SerializeObject(ee));
                                    });
                                }
                                else
                                {
                                    MessageBox.Show("从2029年历史表中获取数据时出现错误！" + result2029.strMsg);
                                    return;
                                }
                                break;
                            case 2030:
                                var result2030 = respository.SearchDataFrom2030YearHistoryTable(conditions, context.tb_workpiecedatacapture_history_2030);
                                if (result2030.bSuccess)
                                {
                                    result2030.obj_Return.ForEach(ee =>
                                    {
                                        list_FinalJson.Add(JsonConvert.SerializeObject(ee));
                                    });
                                }
                                else
                                {
                                    MessageBox.Show("从2030年历史表中获取数据时出现错误！" + result2030.strMsg);
                                    return;
                                }
                                break;
                            default:
                                result = new FuncResult<List<tb_WorkPieceDataCapture_Realtime>>();
                                result.bSuccess = true;
                                result.obj_Return = new List<tb_WorkPieceDataCapture_Realtime>();
                                break;
                        }
                    }
                    #endregion
                    list_FinalJson.ForEach(ee =>
                    {
                        list_Final.Add(JsonConvert.DeserializeObject<tb_WorkPieceDataCapture_Realtime>(ee));
                    });
                    var total = list_Final.Count;
                    if (total == 0)
                    {
                        MessageBox.Show("当天没有产量");
                        return;
                    }
                    #region 查询出产量统计
                    var ALL_Total = list_Final.Count();
                    var NG_ZT = list_Final.Where(ee => ee.FLJ_ZT_Result == "NG").Count(); //主体NG
                    var NG_YCQ = list_Final.Where(ee => ee.FLJ_YCQ_Result == "NG").Count(); //一次圈NG
                    var NG_GLH = list_Final.Where(ee => ee.FLJ_GLH_Result == "NG").Count(); //隔离环NG
                    var NG_ECQ = list_Final.Where(ee => ee.FLJ_ECQ_Result == "NG").Count(); //二次圈NG
                    var NG_KZH = list_Final.Where(ee => ee.FLJ_KZH_Result == "NG").Count(); //安全环NG
                    var NG_TOTAL = NG_ZT + NG_YCQ + NG_GLH + NG_ECQ + NG_KZH;   //总NG量
                    var NG_ZT_Percent = MathMethods.CalculatePercentage(NG_ZT, NG_TOTAL);   //主体NG百分比
                    var NG_YCQ_Percent = MathMethods.CalculatePercentage(NG_YCQ, NG_TOTAL); //一次圈NG百分比
                    var NG_GLH_Percent = MathMethods.CalculatePercentage(NG_GLH, NG_TOTAL); //隔离环NG百分比
                    var NG_ECQ_Percent = MathMethods.CalculatePercentage(NG_ECQ, NG_TOTAL); //二次圈NG百分比
                    var NG_KZH_Percent = MathMethods.CalculatePercentage(NG_KZH, NG_TOTAL); //安全环NG百分比
                    this.Yield = string.Format("产量：{0}；NG：{1}；主体：{2}({3})；一次圈：{4}({5})；隔离环：{6}({7})；二次圈：{8}({9})；安全环：{10}({11})；", ALL_Total, NG_TOTAL, NG_ZT, NG_ZT_Percent, NG_YCQ, NG_YCQ_Percent, NG_GLH, NG_GLH_Percent, NG_ECQ, NG_ECQ_Percent, NG_KZH, NG_KZH_Percent);
                    #endregion

                    SeriesData = new SeriesCollection();
                    Labels = new List<string> { "小于-1", "(-1到<-0.9)", "(-0.9到<-0.8)", "(-0.8到<-0.7)", "(-0.7到<-0.6)", "(-0.6到<-0.5)", "(-0.5到<-0.4)", "(-0.4到<-0.3)", "(-0.3到<-0.2)", "(-0.2到<-0.1)", "(-0.1到<0.0)", "(0.0到<0.1)", "(0.1到<0.2)", "(0.2到<0.3)", "(0.3到<0.4)", "(0.4到<0.5)", "(0.5到<0.6)", "(0.6到<0.7)", "(0.7到<0.8)", "(0.8到<0.9)", "(0.9到<1)", "大于上限1" };
                    SeriesData = new SeriesCollection { };
                    switch (this.conditions.FLJ_Type)
                    {
                        case "主体":
                            #region 主体
                            ColumnSeries series_ZT = new ColumnSeries();
                            series_ZT.Title = "主体";
                            series_ZT.Fill = new SolidColorBrush(Color.FromRgb(1, 205, 99));
                            series_ZT.DataLabels = true;
                            series_ZT.Foreground = new SolidColorBrush(Color.FromRgb(255, 255, 255));
                            series_ZT.FontSize = 22;
                            //显示文本内容
                            series_ZT.LabelPoint = p => p.Y.ToString();
                            series_ZT.Values = new ChartValues<ObservablePoint>
                            {
                                new ObservablePoint(0, list_Final.Where(ee=>ee.FLJ_ZT_Value< -1).Count()),
                                new ObservablePoint(1,  list_Final.Where(ee=>ee.FLJ_ZT_Value>=-1 && ee.FLJ_ZT_Value<-0.9).Count()),
                                new ObservablePoint(2, list_Final.Where(ee=>ee.FLJ_ZT_Value>=-0.9 && ee.FLJ_ZT_Value<-0.8).Count()),
                                new ObservablePoint(3, list_Final.Where(ee=>ee.FLJ_ZT_Value>=-0.8 && ee.FLJ_ZT_Value<-0.7).Count()),
                                new ObservablePoint(4, list_Final.Where(ee=>ee.FLJ_ZT_Value>=-0.7 && ee.FLJ_ZT_Value<-0.6).Count()),
                                new ObservablePoint(5, list_Final.Where(ee=>ee.FLJ_ZT_Value>=-0.6 && ee.FLJ_ZT_Value<-0.5).Count()),
                                new ObservablePoint(6, list_Final.Where(ee=>ee.FLJ_ZT_Value>=-0.5 && ee.FLJ_ZT_Value<-0.4).Count()),
                                new ObservablePoint(7, list_Final.Where(ee=>ee.FLJ_ZT_Value>=-0.4 && ee.FLJ_ZT_Value<-0.3).Count()),
                                new ObservablePoint(8, list_Final.Where(ee=>ee.FLJ_ZT_Value>=-0.3 && ee.FLJ_ZT_Value<-0.2).Count()),
                                new ObservablePoint(9, list_Final.Where(ee=>ee.FLJ_ZT_Value>=-0.2 && ee.FLJ_ZT_Value<-0.1).Count()),
                                new ObservablePoint(10, list_Final.Where(ee=>ee.FLJ_ZT_Value>=-0.1 && ee.FLJ_ZT_Value<-0.0).Count()),
                                new ObservablePoint(11, list_Final.Where(ee =>ee.FLJ_ZT_Value >= 0.0 && ee.FLJ_ZT_Value < 0.1).Count()),
                                new ObservablePoint(12, list_Final.Where(ee =>ee.FLJ_ZT_Value >= 0.1 && ee.FLJ_ZT_Value < 0.2).Count()),
                                new ObservablePoint(13, list_Final.Where(ee =>ee.FLJ_ZT_Value >= 0.2 && ee.FLJ_ZT_Value < 0.3).Count()),
                                new ObservablePoint(14, list_Final.Where(ee=> ee.FLJ_ZT_Value>=0.3 && ee.FLJ_ZT_Value<0.4).Count()),
                                new ObservablePoint(15, list_Final.Where(ee =>ee.FLJ_ZT_Value >= 0.4 && ee.FLJ_ZT_Value < 0.5).Count()),
                                new ObservablePoint(16, list_Final.Where(ee =>ee.FLJ_ZT_Value >= 0.5 && ee.FLJ_ZT_Value < 0.6).Count()),
                                new ObservablePoint(17, list_Final.Where(ee =>ee.FLJ_ZT_Value >= 0.6 && ee.FLJ_ZT_Value < 0.7).Count()),
                                new ObservablePoint(18, list_Final.Where(ee =>ee.FLJ_ZT_Value >= 0.7 && ee.FLJ_ZT_Value < 0.8).Count()),
                                new ObservablePoint(19, list_Final.Where(ee =>ee.FLJ_ZT_Value >= 0.8 && ee.FLJ_ZT_Value < 0.9).Count()),
                                new ObservablePoint(20, list_Final.Where(ee =>ee.FLJ_ZT_Value >= 0.9 && ee.FLJ_ZT_Value < 1.0).Count()),
                                new ObservablePoint(21, list_Final.Where(ee =>ee.FLJ_ZT_Value >= 1).Count())
                            };
                            //柱子宽度
                            series_ZT.Width = 50;
                            series_ZT.MaxColumnWidth = 50;
                            #endregion
                            SeriesData.Add(series_ZT);          //主体
                            break;
                        case "隔离环":
                            #region 隔离环
                            ColumnSeries series_GLH = new ColumnSeries();

                            series_GLH.Title = "隔离环";
                            series_GLH.Fill = new SolidColorBrush(Color.FromRgb(182, 26, 174));
                            series_GLH.DataLabels = true;
                            series_GLH.Foreground = new SolidColorBrush(Color.FromRgb(255, 255, 255));
                            series_GLH.FontSize = 22;
                            //显示文本内容
                            series_GLH.LabelPoint = p => p.Y.ToString();
                            series_GLH.Values = new ChartValues<ObservablePoint>
                            {
                                new ObservablePoint(0, list_Final.Where(ee=>ee.FLJ_GLH_Value< -1).Count()),
                                new ObservablePoint(1,  list_Final.Where(ee=>ee.FLJ_GLH_Value>=-1 && ee.FLJ_GLH_Value<-0.9).Count()),
                                new ObservablePoint(2, list_Final.Where(ee=>ee.FLJ_GLH_Value>=-0.9 && ee.FLJ_GLH_Value<-0.8).Count()),
                                new ObservablePoint(3, list_Final.Where(ee=>ee.FLJ_GLH_Value>=-0.8 && ee.FLJ_GLH_Value<-0.7).Count()),
                                new ObservablePoint(4, list_Final.Where(ee=>ee.FLJ_GLH_Value>=-0.7 && ee.FLJ_GLH_Value<-0.6).Count()),
                                new ObservablePoint(5, list_Final.Where(ee=>ee.FLJ_GLH_Value>=-0.6 && ee.FLJ_GLH_Value<-0.5).Count()),
                                new ObservablePoint(6, list_Final.Where(ee=>ee.FLJ_GLH_Value>=-0.5 && ee.FLJ_GLH_Value<-0.4).Count()),
                                new ObservablePoint(7, list_Final.Where(ee=>ee.FLJ_GLH_Value>=-0.4 && ee.FLJ_GLH_Value<-0.3).Count()),
                                new ObservablePoint(8, list_Final.Where(ee=>ee.FLJ_GLH_Value>=-0.3 && ee.FLJ_GLH_Value<-0.2).Count()),
                                new ObservablePoint(9, list_Final.Where(ee=>ee.FLJ_GLH_Value>=-0.2 && ee.FLJ_GLH_Value<-0.1).Count()),
                                new ObservablePoint(10, list_Final.Where(ee=>ee.FLJ_GLH_Value>=-0.1 && ee.FLJ_GLH_Value<-0.0).Count()),
                                new ObservablePoint(11, list_Final.Where(ee =>ee.FLJ_GLH_Value >= 0.0 && ee.FLJ_GLH_Value < 0.1).Count()),
                                new ObservablePoint(12, list_Final.Where(ee =>ee.FLJ_GLH_Value >= 0.1 && ee.FLJ_GLH_Value < 0.2).Count()),
                                new ObservablePoint(13, list_Final.Where(ee =>ee.FLJ_GLH_Value >= 0.2 && ee.FLJ_GLH_Value < 0.3).Count()),
                                new ObservablePoint(14, list_Final.Where(ee=>ee.FLJ_GLH_Value>=0.3 && ee.FLJ_GLH_Value<0.4).Count()),
                                new ObservablePoint(15, list_Final.Where(ee =>ee.FLJ_GLH_Value >= 0.4 && ee.FLJ_GLH_Value < 0.5).Count()),
                                new ObservablePoint(16, list_Final.Where(ee =>ee.FLJ_GLH_Value >= 0.5 && ee.FLJ_GLH_Value < 0.6).Count()),
                                new ObservablePoint(17, list_Final.Where(ee =>ee.FLJ_GLH_Value >= 0.6 && ee.FLJ_GLH_Value < 0.7).Count()),
                                new ObservablePoint(18, list_Final.Where(ee =>ee.FLJ_GLH_Value >= 0.7 && ee.FLJ_GLH_Value < 0.8).Count()),
                                new ObservablePoint(19, list_Final.Where(ee =>ee.FLJ_GLH_Value >= 0.8 && ee.FLJ_GLH_Value < 0.9).Count()),
                                new ObservablePoint(20, list_Final.Where(ee =>ee.FLJ_GLH_Value >= 0.9 && ee.FLJ_GLH_Value < 1.0).Count()),
                                new ObservablePoint(21, list_Final.Where(ee =>ee.FLJ_GLH_Value >= 1).Count())
                            };
                            //柱子宽度
                            series_GLH.Width = 50;
                            series_GLH.MaxColumnWidth = 50;
                            #endregion
                            SeriesData.Add(series_GLH);     //隔离环
                            break;
                        case "二次圈":
                            #region 二次圈
                            ColumnSeries series_ECQ = new ColumnSeries();
                            series_ECQ.Title = "二次圈";
                            series_ECQ.Fill = new SolidColorBrush(Color.FromRgb(17, 153, 158));
                            series_ECQ.DataLabels = true;
                            series_ECQ.Foreground = new SolidColorBrush(Color.FromRgb(255, 255, 255));
                            series_ECQ.FontSize = 22;
                            //显示文本内容
                            series_ECQ.LabelPoint = p => p.Y.ToString();
                            series_ECQ.Values = new ChartValues<ObservablePoint>
                            {
                                new ObservablePoint(0, list_Final.Where(ee=>ee.FLJ_ECQ_Value< -1).Count()),
                                new ObservablePoint(1,  list_Final.Where(ee=>ee.FLJ_ECQ_Value>=-1 && ee.FLJ_ECQ_Value<-0.9).Count()),
                                new ObservablePoint(2, list_Final.Where(ee=>ee.FLJ_ECQ_Value>=-0.9 && ee.FLJ_ECQ_Value<-0.8).Count()),
                                new ObservablePoint(3, list_Final.Where(ee=>ee.FLJ_ECQ_Value>=-0.8 && ee.FLJ_ECQ_Value<-0.7).Count()),
                                new ObservablePoint(4, list_Final.Where(ee=>ee.FLJ_ECQ_Value>=-0.7 && ee.FLJ_ECQ_Value<-0.6).Count()),
                                new ObservablePoint(5, list_Final.Where(ee=>ee.FLJ_ECQ_Value>=-0.6 && ee.FLJ_ECQ_Value<-0.5).Count()),
                                new ObservablePoint(6, list_Final.Where(ee=>ee.FLJ_ECQ_Value>=-0.5 && ee.FLJ_ECQ_Value<-0.4).Count()),
                                new ObservablePoint(7, list_Final.Where(ee=>ee.FLJ_ECQ_Value>=-0.4 && ee.FLJ_ECQ_Value<-0.3).Count()),
                                new ObservablePoint(8, list_Final.Where(ee=>ee.FLJ_ECQ_Value>=-0.3 && ee.FLJ_ECQ_Value<-0.2).Count()),
                                new ObservablePoint(9, list_Final.Where(ee=>ee.FLJ_ECQ_Value>=-0.2 && ee.FLJ_ECQ_Value<-0.1).Count()),
                                new ObservablePoint(10, list_Final.Where(ee=>ee.FLJ_ECQ_Value>=-0.1 && ee.FLJ_ECQ_Value<-0.0).Count()),
                                new ObservablePoint(11, list_Final.Where(ee => ee.FLJ_ECQ_Value >= 0.0 && ee.FLJ_ECQ_Value < 0.1).Count()),
                                new ObservablePoint(12, list_Final.Where(ee => ee.FLJ_ECQ_Value >= 0.1 && ee.FLJ_ECQ_Value < 0.2).Count()),
                                new ObservablePoint(13, list_Final.Where(ee => ee.FLJ_ECQ_Value >= 0.2 && ee.FLJ_ECQ_Value < 0.3).Count()),
                                new ObservablePoint(14, list_Final.Where(ee=>ee.FLJ_ECQ_Value>=0.3 && ee.FLJ_ECQ_Value<0.4).Count()),
                                new ObservablePoint(15, list_Final.Where(ee => ee.FLJ_ECQ_Value >= 0.4 && ee.FLJ_ECQ_Value < 0.5).Count()),
                                new ObservablePoint(16, list_Final.Where(ee =>ee.FLJ_ECQ_Value >= 0.5 && ee.FLJ_ECQ_Value < 0.6).Count()),
                                new ObservablePoint(17, list_Final.Where(ee =>ee.FLJ_ECQ_Value >= 0.6 && ee.FLJ_ECQ_Value < 0.7).Count()),
                                new ObservablePoint(18, list_Final.Where(ee =>ee.FLJ_ECQ_Value >= 0.7 && ee.FLJ_ECQ_Value < 0.8).Count()),
                                new ObservablePoint(19, list_Final.Where(ee =>ee.FLJ_ECQ_Value >= 0.8 && ee.FLJ_ECQ_Value < 0.9).Count()),
                                new ObservablePoint(20, list_Final.Where(ee => ee.FLJ_ECQ_Value >= 0.9 && ee.FLJ_ECQ_Value < 1.0).Count()),
                                new ObservablePoint(21, list_Final.Where(ee =>ee.FLJ_ECQ_Value >= 1).Count())
                            };
                            //柱子宽度
                            series_ECQ.Width = 50;
                            series_ECQ.MaxColumnWidth = 50;
                            #endregion
                            SeriesData.Add(series_ECQ);     //二次圈
                            break;
                        case "安全环":
                            #region 安全环
                            ColumnSeries series_KZH = new ColumnSeries();
                            series_KZH.Title = "安全环";
                            series_KZH.Fill = new SolidColorBrush(Color.FromRgb(242, 93, 156));
                            series_KZH.DataLabels = true;
                            series_KZH.Foreground = new SolidColorBrush(Color.FromRgb(255, 255, 255));
                            series_KZH.FontSize = 22;
                            //显示文本内容
                            series_KZH.LabelPoint = p => p.Y.ToString();
                            series_KZH.Values = new ChartValues<ObservablePoint>
                            {
                                new ObservablePoint(0, list_Final.Where(ee=>ee.FLJ_KZH_Value< -1).Count()),
                                new ObservablePoint(1,  list_Final.Where(ee=>ee.FLJ_KZH_Value>=-1 && ee.FLJ_KZH_Value<-0.9).Count()),
                                new ObservablePoint(2, list_Final.Where(ee=>ee.FLJ_KZH_Value>=-0.9 && ee.FLJ_KZH_Value<-0.8).Count()),
                                new ObservablePoint(3, list_Final.Where(ee=>ee.FLJ_KZH_Value>=-0.8 && ee.FLJ_KZH_Value<-0.7).Count()),
                                new ObservablePoint(4, list_Final.Where(ee=>ee.FLJ_KZH_Value>=-0.7 && ee.FLJ_KZH_Value<-0.6).Count()),
                                new ObservablePoint(5, list_Final.Where(ee=>ee.FLJ_KZH_Value>=-0.6 && ee.FLJ_KZH_Value<-0.5).Count()),
                                new ObservablePoint(6, list_Final.Where(ee=>ee.FLJ_KZH_Value>=-0.5 && ee.FLJ_KZH_Value<-0.4).Count()),
                                new ObservablePoint(7, list_Final.Where(ee=>ee.FLJ_KZH_Value>=-0.4 && ee.FLJ_KZH_Value<-0.3).Count()),
                                new ObservablePoint(8, list_Final.Where(ee=>ee.FLJ_KZH_Value>=-0.3 && ee.FLJ_KZH_Value<-0.2).Count()),
                                new ObservablePoint(9, list_Final.Where(ee=>ee.FLJ_KZH_Value>=-0.2 && ee.FLJ_KZH_Value<-0.1).Count()),
                                new ObservablePoint(10, list_Final.Where(ee=>ee.FLJ_KZH_Value>=-0.1 && ee.FLJ_KZH_Value<-0.0).Count()),
                                new ObservablePoint(11, list_Final.Where(ee => ee.FLJ_KZH_Value >= 0.0 && ee.FLJ_KZH_Value < 0.1).Count()),
                                new ObservablePoint(12, list_Final.Where(ee => ee.FLJ_KZH_Value >= 0.1 && ee.FLJ_KZH_Value < 0.2).Count()),
                                new ObservablePoint(13, list_Final.Where(ee => ee.FLJ_KZH_Value >= 0.2 && ee.FLJ_KZH_Value < 0.3).Count()),
                                new ObservablePoint(14, list_Final.Where(ee=>ee.FLJ_KZH_Value>=0.3 && ee.FLJ_KZH_Value<0.4).Count()),
                                new ObservablePoint(15, list_Final.Where(ee =>ee.FLJ_KZH_Value >= 0.4 && ee.FLJ_KZH_Value < 0.5).Count()),
                                new ObservablePoint(16, list_Final.Where(ee =>ee.FLJ_KZH_Value >= 0.5 && ee.FLJ_KZH_Value < 0.6).Count()),
                                new ObservablePoint(17, list_Final.Where(ee =>ee.FLJ_KZH_Value >= 0.6 && ee.FLJ_KZH_Value < 0.7).Count()),
                                new ObservablePoint(18, list_Final.Where(ee =>ee.FLJ_KZH_Value >= 0.7 && ee.FLJ_KZH_Value < 0.8).Count()),
                                new ObservablePoint(19, list_Final.Where(ee =>ee.FLJ_KZH_Value >= 0.8 && ee.FLJ_KZH_Value < 0.9).Count()),
                                new ObservablePoint(20, list_Final.Where(ee => ee.FLJ_KZH_Value >= 0.9 && ee.FLJ_KZH_Value < 1.0).Count()),
                                new ObservablePoint(21, list_Final.Where(ee =>ee.FLJ_KZH_Value >= 1).Count())
                            };
                            //柱子宽度
                            series_KZH.Width = 50;
                            series_KZH.MaxColumnWidth = 50;
                            #endregion
                            SeriesData.Add(series_KZH);    //安全环  
                            break;
                        default:
                            break;
                    }
                }
                else
                {
                    MessageBox.Show("当前无法连接到数据库，请重试一次");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("执行查询操作发生异常！\n" + ex.Message);
            }
        }
        /// <summary>
        /// 执行导出列表数据
        /// </summary>
        /// <param name="parameter"></param>
        private void doExport(object parameter)
        {

        }
        #endregion
        #region 方法

        #endregion
    }
}
