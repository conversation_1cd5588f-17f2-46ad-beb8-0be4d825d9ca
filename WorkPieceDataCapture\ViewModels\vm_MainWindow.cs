﻿using HardwareLibrary.MQTT;
using Microsoft.Extensions.DependencyInjection;
using Models;
using System.ComponentModel;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using WorkPieceDataCapture.ViewModels;
using WorkPieceDataCapture.Views;

namespace ViewModels
{
    public class vm_MainWindow : vm_Base
    {
        ServiceProvider serviceProvider;
        public ICommand MenuSelectCommand { get; set; }
        private uc_WorkPieceDataCapture_RealTime? _realtime;
        private uc_WorkPieceDataCapture_Search? _search;
        private uc_Chart_NG? _chartNG;
        private uc_Chart_Range? _ChartRange;
        private UserControl _uc;
        public UserControl? uc
        {
            get { return _uc; }
            set
            {
                _uc = value;
                OnPropertyChanged("uc");
            }
        }

        public vm_MainWindow(IServiceProvider serviceProvider, MQTT_Client mqttclient)
        {
            #region 连接MQTT
            mqttclient.topicsNeedSubscribe = new List<string>() { "12.61S生产数据" };
            var result = mqttclient.ConnectToServer();
            if (!result.bSuccess)
            {
                MessageBox.Show("MQTT未连接，请检查位于" + mqttclient.model_login.strIPAddress + ":" + mqttclient.model_login.intPort.ToString() + "的MQTT服务是否已开启");
                Application.Current.Shutdown();
            }
            #endregion
            #region 子界面注册
            //实时生产数据采集界面
            _realtime = new uc_WorkPieceDataCapture_RealTime();
            var vm_realtime = serviceProvider.GetService<vm_WorkPieceDatacapture_RealTime>();
            if (vm_realtime != null)
            {
                _realtime.DataContext = vm_realtime;
            }
            //历史数据查询界面
            _search = new uc_WorkPieceDataCapture_Search();
            var vm_search = serviceProvider.GetService<vm_WorkPieceDatacapture_Search>();
            if (vm_search != null)
            {
                _search.DataContext = vm_search;
            }
            //NG饼状图界面
            _chartNG = new uc_Chart_NG();
            var vm_Chart_NG = serviceProvider.GetService<vm_Chart_NG>();
            if (vm_Chart_NG != null)
            {
                _chartNG.DataContext = vm_Chart_NG;
            }
            //柱状图界面
            _ChartRange = new uc_Chart_Range();
            var vm_chartRange = serviceProvider.GetService<vm_Chart_Range>();
            if (vm_chartRange != null)
            {
                _ChartRange.DataContext = vm_chartRange;
            }
            #endregion
            #region 事件注册
            this.MenuSelectCommand = new RelayCommand(MenuSelected);
            #endregion

        }
        //菜单按钮选择事件处理
        private void MenuSelected(object parameter)
        {
            string strButtonName = parameter as string;
            switch (strButtonName)
            {
                case "实时数据采集":
                    this.uc = _realtime;
                    break;
                case "历史数据查询":
                    this.uc = _search;
                    break;
                case "NG占比分析":
                    this.uc = _chartNG;
                    break;
                case "NG数量分布":
                    this.uc = _ChartRange;
                    break;
                case "坐标数据图表":
                    // 打开坐标数据测试窗口
                    var testWindow = new CoordinateDataTestWindow();
                    testWindow.Owner = Application.Current.MainWindow;
                    testWindow.ShowDialog();
                    break;
            }
        }
    }
}
