﻿using NPOI.HSSF.UserModel;
using NPOI.HSSF.Util;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using System.Reflection;

namespace Utils
{
    public class ExcelHelper
    {
        public void WriteEntityListToExcel<T>(List<T> entityList, string filePath, string sheetName, Dictionary<string, int> dic_ColumnRelation, int startRow) where T : class
        {
            IWorkbook workbook;
            using (FileStream file = new FileStream(filePath, FileMode.Open, FileAccess.Read))
            {
                // 创建新的Excel工作簿
                workbook = new XSSFWorkbook(file);
            }
            // 创建工作表
            ISheet sheet = workbook.GetSheet(sheetName);
            // 获取实体类的属性列表
            PropertyInfo[] properties = typeof(T).GetProperties();
            //// 写入表头
            //IRow headerRow = sheet.CreateRow(0);
            //for (int i = 0; i < properties.Length; i++)
            //{
            //    ICell cell = headerRow.CreateCell(i);
            //    cell.SetCellValue(properties[i].Name);
            //}

            // 写入实体数据
            foreach (T entity in entityList)
            {
                IRow row = sheet.CreateRow(startRow++);
                for (int i = 0; i < properties.Length; i++)
                {
                    if (dic_ColumnRelation.ContainsKey(properties[i].Name))
                    {
                        ICell cell = row.CreateCell(dic_ColumnRelation[properties[i].Name]);
                        object value = properties[i].GetValue(entity);
                        cell.SetCellValue(value?.ToString() ?? "");
                    }
                }
            }
            using (FileStream file = new FileStream(filePath, FileMode.Open, FileAccess.Write))
            {
                // 保存工作簿到文件
                workbook.Write(file);
            }
            // 关闭工作簿资源
            workbook.Close();
        }
        public void WriteEntityDetailToExcel<T>(T entity, string filePath, string sheetName, Dictionary<string, int> dic_RowRelation) where T : class
        {
            IWorkbook workbook;
            using (FileStream file = new FileStream(filePath, FileMode.Open, FileAccess.Read))
            {
                // 创建新的Excel工作簿
                workbook = new HSSFWorkbook(file);
            }
            // 创建工作表
            ISheet sheet = workbook.GetSheet(sheetName);
            // 获取实体类的属性列表
            PropertyInfo[] properties = typeof(T).GetProperties();

            // 写入实体数据
            for (int i = 0; i < properties.Length; i++)
            {
                if (dic_RowRelation.ContainsKey(properties[i].Name))
                {
                    IRow row = sheet.GetRow(dic_RowRelation[properties[i].Name]);
                    ICell cell = row.GetCell(2);
                    object value = properties[i].GetValue(entity);
                    HSSFRichTextString rts = new HSSFRichTextString(value?.ToString() ?? "");
                    IFont font = workbook.CreateFont();
                    if ((value?.ToString() ?? "") == "OK")
                    {
                        font.Color = HSSFColor.Blue.Index;
                    }
                    else if ((value?.ToString() ?? "") == "NG")
                    {
                        font.Color = HSSFColor.Red.Index;
                    }
                    rts.ApplyFont(font);
                    cell.SetCellValue(rts);
                }
            }
            using (FileStream file = new FileStream(filePath, FileMode.Open, FileAccess.Write))
            {
                // 保存工作簿到文件
                workbook.Write(file);
            }
            // 关闭工作簿资源
            workbook.Close();
        }
    }
}
