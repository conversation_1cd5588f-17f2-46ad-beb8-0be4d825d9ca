﻿using Interfaces.Model;

namespace Models
{
    //方法的执行结果
    public class FuncResult<T> : IFuncResult<T>
    {
        public bool bSuccess { get; set; }
        public string strMsg { get; set; }
        public T obj_Return { get; set; }

        public void setException(Exception ex)
        {
            this.bSuccess = false;
            this.strMsg = ex.Message + ex.InnerException ?? ex.InnerException.Message;
        }
    }
}
