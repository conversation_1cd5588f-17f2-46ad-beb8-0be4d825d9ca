﻿using Models;
using System.Collections.Specialized;
using System.ComponentModel;

namespace WorkPieceDataCapture.ViewModels
{
    public class vm_Base: INotifyPropertyChanged
    {
        #region MVVM方法
        public event PropertyChangedEventHandler? PropertyChanged;
        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
        #endregion
    }
}
