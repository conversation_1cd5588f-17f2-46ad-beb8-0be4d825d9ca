﻿﻿using Models;
using System.Windows;
using ViewModels;

namespace WorkPieceDataCapture.Views
{
    /// <summary>
    /// CoordinateChartWindow.xaml 的交互逻辑
    /// </summary>
    public partial class CoordinateChartWindow : Window
    {
        public CoordinateChartWindow()
        {
            InitializeComponent();
        }

        /// <summary>
        /// 使用坐标数据初始化窗口
        /// </summary>
        /// <param name="data">包含坐标数据的实体</param>
        public CoordinateChartWindow(tb_WorkPieceDataCapture_Realtime data) : this()
        {
            this.DataContext = new vm_Chart_Coordinate(data);
        }
    }
}
