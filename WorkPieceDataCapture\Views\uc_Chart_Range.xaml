﻿<UserControl x:Class="WorkPieceDataCapture.Views.uc_Chart_Range" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
 xmlns:d="http://schemas.microsoft.com/expression/blend/2008" xmlns:lvc="clr-namespace:LiveCharts.Wpf;assembly=LiveCharts.Wpf" mc:Ignorable="d"  d:DesignHeight="1000" d:DesignWidth="1920">
    <Viewbox Stretch="Fill">
        <Grid Style="{DynamicResource GeneralGrid}" Height="1100" Width="2220">
            <Grid.RowDefinitions>
                <RowDefinition Height="170"></RowDefinition>
                <RowDefinition Height="*"></RowDefinition>
                <RowDefinition Height="200"></RowDefinition>
            </Grid.RowDefinitions>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="35"></ColumnDefinition>
                <ColumnDefinition Width="390"></ColumnDefinition>
                <ColumnDefinition Width="35"></ColumnDefinition>
                <ColumnDefinition Width="*"></ColumnDefinition>
                <ColumnDefinition Width="*"></ColumnDefinition>
                <ColumnDefinition Width="*"></ColumnDefinition>
                <ColumnDefinition Width="*"></ColumnDefinition>
            </Grid.ColumnDefinitions>
            <Label Content="生&#xA;产&#xA;日&#xA;期" Style="{DynamicResource GroupTitle}"  ></Label>
            <WrapPanel Grid.Row="0" Grid.Column="1" Grid.ColumnSpan="100">
                <Calendar Name="begin" SelectedDate="{Binding conditions.dDate_Begin}" PreviewMouseUp="Calendar_PreviewMouseUp"></Calendar>
                <Label Style="{DynamicResource DataDetailFieldTitle_4Words}" Content="至"  Width="25"/>
                <Calendar Name="end" SelectedDate="{Binding conditions.dDate_End}" PreviewMouseUp="Calendar_PreviewMouseUp"></Calendar>
            </WrapPanel>
            <Label Content="查&#xA;询&#xA;条&#xA;件" Style="{DynamicResource GroupTitle}" Grid.Column="2"></Label>
            <Grid Grid.Row="0" Grid.Column="3">
                <Grid.RowDefinitions>
                    <RowDefinition></RowDefinition>
                    <RowDefinition></RowDefinition>
                    <RowDefinition></RowDefinition>
                </Grid.RowDefinitions>
                <WrapPanel VerticalAlignment="Center">
                    <Label Content="总成型号" Style="{DynamicResource DataDetailFieldTitle_4Words}"></Label>
                    <TextBox Name="txtModel" Style="{DynamicResource TextBox_Normal}" Text="{Binding conditions.ZC_Model}" Grid.Row="3" Height="30" Width="290" Margin="5"></TextBox>
                </WrapPanel>
                <WrapPanel Grid.Row="1" VerticalAlignment="Center">
                    <Label Content="部件类别" Style="{DynamicResource DataDetailFieldTitle_4Words}"></Label>
                    <ComboBox FontSize="20"  Text="{Binding conditions.FLJ_Type}" Height="30" Width="290" Margin="5" >
                        <ComboBoxItem Content="主体"/>
                        <ComboBoxItem Content="隔离环" />
                        <ComboBoxItem Content="二次圈" />
                        <ComboBoxItem Content="安全环" />
                    </ComboBox>
                </WrapPanel>
                <WrapPanel Grid.Row="2" VerticalAlignment="Center" HorizontalAlignment="Center">
                    <Button Style="{DynamicResource GreenButton}" Content="统计" Click="Button_Click" ></Button>
                    <Button Style="{DynamicResource BlueButton}" Content="导出" Click="Button_Click" ></Button>
                </WrapPanel>

            </Grid>
            <Label Content="检测数值分布" Grid.Row="0" Grid.ColumnSpan="100" VerticalContentAlignment="Bottom" FontSize="22" FontWeight="Bold" Foreground="White" HorizontalAlignment="Center" ></Label>
            <lvc:CartesianChart Name="aaa" Series="{Binding SeriesData}"  Grid.Row="1" Grid.Column="0" Grid.ColumnSpan="100" Margin="10,15,15,15" LegendLocation="Bottom"  Foreground="White" Zoom="X">
                <lvc:CartesianChart.AxisX>
                    <lvc:Axis Labels="{Binding Labels}" FontSize="12" LabelsRotation="0" DisableAnimations="True">
                        <lvc:Axis.Separator>
                            <lvc:Separator Visibility="Visible" Step="1" StrokeThickness="1" StrokeDashArray="3" Stroke="#404F56" >
                            </lvc:Separator>
                        </lvc:Axis.Separator>
                    </lvc:Axis>
                </lvc:CartesianChart.AxisX>
                <lvc:CartesianChart.AxisY>
                    <lvc:Axis>
                        <lvc:Axis.Separator>
                            <lvc:Separator Visibility="Visible" StrokeThickness="1" StrokeDashArray="3" Stroke="#404F56" >
                            </lvc:Separator>
                        </lvc:Axis.Separator>
                    </lvc:Axis>
                </lvc:CartesianChart.AxisY>
                <lvc:CartesianChart.DataTooltip>
                    <lvc:DefaultTooltip ContentStringFormat="">

                    </lvc:DefaultTooltip>
                </lvc:CartesianChart.DataTooltip>
            </lvc:CartesianChart>
            <Label Content="{Binding Yield}" Grid.Row="2" Grid.Column="0" Grid.ColumnSpan="100"  FontSize="16" FontWeight="Bold" Foreground="White" HorizontalAlignment="Center" ></Label>
        </Grid>
    </Viewbox>
</UserControl>
