﻿using Microsoft.EntityFrameworkCore;
using Models;
using System.ComponentModel.DataAnnotations.Schema;

public class AppDbContext : DbContext
{
    public AppDbContext(DbContextOptions<AppDbContext> options)
        : base(options)
    {
    }
    public DbSet<tb_WorkPieceDataCapture_Realtime> tb_workpiecedatacapture_realtime { get; set; }
    public DbSet<tb_WorkPieceDataCapture_History_2024> tb_workpiecedatacapture_history_2024 { get; set; }
    public DbSet<tb_WorkPieceDataCapture_History_2025> tb_workpiecedatacapture_history_2025 { get; set; }
    public DbSet<tb_WorkPieceDataCapture_History_2026> tb_workpiecedatacapture_history_2026 { get; set; }
    public DbSet<tb_WorkPieceDataCapture_History_2027> tb_workpiecedatacapture_history_2027 { get; set; }
    public DbSet<tb_WorkPieceDataCapture_History_2028> tb_workpiecedatacapture_history_2028 { get; set; }
    public DbSet<tb_WorkPieceDataCapture_History_2029> tb_workpiecedatacapture_history_2029 { get; set; }
    public DbSet<tb_WorkPieceDataCapture_History_2030> tb_workpiecedatacapture_history_2030 { get; set; }
    // 其他 DbSet 属性...
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<tb_WorkPieceDataCapture_Realtime>().ToTable("tb_WorkPieceDataCapture_Realtime")
            .HasKey(e => e.iID); // 指定Id为主键
        modelBuilder.Entity<tb_WorkPieceDataCapture_History_2024>().ToTable("tb_WorkPieceDataCapture_History_2024")
            .HasKey(e => e.iID); // 指定Id为主键
        modelBuilder.Entity<tb_WorkPieceDataCapture_History_2025>().ToTable("tb_WorkPieceDataCapture_History_2025")
            .HasKey(e => e.iID); // 指定Id为主键
        modelBuilder.Entity<tb_WorkPieceDataCapture_History_2026>().ToTable("tb_WorkPieceDataCapture_History_2026")
            .HasKey(e => e.iID); // 指定Id为主键
        modelBuilder.Entity<tb_WorkPieceDataCapture_History_2027>().ToTable("tb_WorkPieceDataCapture_History_2027")
            .HasKey(e => e.iID); // 指定Id为主键
        modelBuilder.Entity<tb_WorkPieceDataCapture_History_2028>().ToTable("tb_WorkPieceDataCapture_History_2028")
            .HasKey(e => e.iID); // 指定Id为主键
        modelBuilder.Entity<tb_WorkPieceDataCapture_History_2029>().ToTable("tb_WorkPieceDataCapture_History_2029")
            .HasKey(e => e.iID); // 指定Id为主键
        modelBuilder.Entity<tb_WorkPieceDataCapture_History_2030>().ToTable("tb_WorkPieceDataCapture_History_2030")
            .HasKey(e => e.iID); // 指定Id为主键

        // 其他配置...
    }

}