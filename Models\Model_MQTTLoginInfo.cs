﻿namespace Models
{
    /// <summary>
    /// MQTT服务器登录信息
    /// </summary>
    public class Model_MQTTLoginInfo
    {
        public string strClientID { get; set; }                                        //客户端ID
        public string strIPAddress { get; set; }                                        //服务器IP地址
        public int intPort { get; set; } = -1;                                           //服务器端口号
        public string strLoginUserName { get; set; }                                    //服务器登录用户名
        public string strLoginPassword { get; set; }                                    //服务器登录密码
        public string strCubeSubscribeTopic { get; set; }                               //订阅迅达Cube连接主题
        public string strEquipSubscribeTopic { get; set; }                                   //订阅设备数据主题
    }
}
