<Window x:Class="WorkPieceDataCapture.Views.CoordinateChartWindow" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" xmlns:lvc="clr-namespace:LiveCharts.Wpf;assembly=LiveCharts.Wpf"
 mc:Ignorable="d" Title="安全环压力曲线图" Height="400" Width="1200" WindowStartupLocation="CenterScreen">
    <Grid Style="{DynamicResource GeneralGrid}">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <StackPanel Grid.Row="0" Margin="10">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition></RowDefinition>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition></ColumnDefinition>
                    <ColumnDefinition></ColumnDefinition>
                </Grid.ColumnDefinitions>
            </Grid>
            <TextBlock  Grid.ColumnSpan="2" Grid.Row="0" Text="{Binding Title}" FontSize="20" FontWeight="Bold" Foreground="White" HorizontalAlignment="Center" Margin="0,0,0,10"/>
            <TextBlock Grid.Column="1" Grid.Row="0" Text="{Binding SerialNo, StringFormat=总成标识码: {0}}" FontSize="14" Foreground="White" HorizontalAlignment="Right"/>
        </StackPanel>

        <lvc:CartesianChart Grid.Row="1" Series="{Binding SeriesCollection}" LegendLocation="None" Margin="10" DisableAnimations="True">
            <lvc:CartesianChart.AxisX>
                <lvc:Axis Title="PLS" MinValue="{Binding XAxisMinValue}" FontSize="15" FontWeight="Bold">
                    <lvc:Axis.Separator>
                        <lvc:Separator StrokeThickness="1" StrokeDashArray="2" />
                    </lvc:Axis.Separator>
                </lvc:Axis>
            </lvc:CartesianChart.AxisX>
            <lvc:CartesianChart.AxisY>
                <lvc:Axis Title="&#x200B; "  MinValue="0" FontSize="15" FontWeight="Bold">
                    <lvc:Axis.Separator>
                        <lvc:Separator StrokeThickness="1" StrokeDashArray="4" />
                    </lvc:Axis.Separator>
                </lvc:Axis>
            </lvc:CartesianChart.AxisY>
            <lvc:CartesianChart.DataTooltip>
                <lvc:DefaultTooltip SelectionMode="SharedXValues" />
            </lvc:CartesianChart.DataTooltip>
        </lvc:CartesianChart>
        <Label Content="N" Foreground="DarkGray" FontSize="15" FontWeight="Bold" Grid.Row="1" VerticalContentAlignment="Center" Margin="5,0,0,30"></Label>
    </Grid>
</Window>
