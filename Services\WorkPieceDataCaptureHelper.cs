﻿using HardwareLibrary.MQTT;
using Interfaces.Library;
using Interfaces.Model;
using Models;
using Newtonsoft.Json;
using NLog;
using System.Globalization;
using System.Reflection;

namespace Services
{
    /// <summary>
    /// 数据采集的工具类
    /// </summary>
    public class WorkPieceDataCaptureHelper : IWorkPieceDataCaptureLibrary
    {
        private IServiceProvider _serviceProvider;
        private MQTT_Client mqtt;
        Logger log = LogManager.GetCurrentClassLogger();
        WorkPieceDataCaptureRespository? respository;    //数据库操作类
        List<tb_WorkPieceDataCapture_Realtime>? cache_DataCapture;  //待保存数据的缓存
        Thread? thread_SaveData; //保存数据的线程
        bool switch_thread_SaveData = true; //保存数据的线程开关
        Thread? thread_CheckPLCState;    //检测PLC连接状态的线程
        bool switch_thread_CheckPLCState = true;    //检测PLC连接状态的线程开关
        plcConnectionState? plcState;    //plc的连接状态
        public delegate void feedbackNewestSavedData(tb_WorkPieceDataCapture_Realtime model);    //向调用者反馈最新采集到的数据的代理方法
        public event feedbackNewestSavedData? evt_FeedingNewestData; //向调用者反馈最新采集的数据的事件
        public delegate void feedbackNewestSummaryData(tb_WorkPieceDataCapture_Realtime model);    //向调用者反馈最新产量统计的代理方法
        public event feedbackNewestSummaryData? evt_FeedingNewestSummary; //向调用者反馈最新产量统计的事件


        public delegate void feedbackPLCConnectionState(plcConnectionState model); //向调用者反馈PLC连接状态的代理方法
        public event feedbackPLCConnectionState? evt_FeedingState; //向调用者反馈PLC连接状态的事件
        private string strLastSerialNo = "";    //记录一下上一次保存的数据序列号
        /// <summary>
        /// 构造函数
        /// </summary>
        public WorkPieceDataCaptureHelper(IServiceProvider serviceProvider, MQTT_Client mqttClient)
        {
            this._serviceProvider = serviceProvider;
            this.mqtt = mqttClient;
            this.initialize();
        }
        ~WorkPieceDataCaptureHelper()
        {
            this.switch_thread_CheckPLCState = false;
            this.switch_thread_SaveData = false;
        }
        /// <summary>
        /// 界面初始化
        /// </summary>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public IFuncResult<string> initialize()
        {
            var result = new FuncResult<string>();
            try
            {
                this.respository = new WorkPieceDataCaptureRespository(this._serviceProvider);
                //初始化数据缓存容器
                cache_DataCapture = new List<tb_WorkPieceDataCapture_Realtime>();
                //绑定MQTT事件
                this.mqtt.onMQTTMessageReceived += this.MQTT_MessageReceived;
                //启动读数据的线程
                this.thread_CheckPLCState = new Thread(this.doCheckMQTTState);
                this.thread_CheckPLCState.IsBackground = true;
                this.thread_CheckPLCState.Start();
                //启动保存数据的线程
                this.thread_SaveData = new Thread(this.doSaveData);
                this.thread_SaveData.IsBackground = true;
                this.thread_SaveData.Start();
                result.bSuccess = true;
            }
            catch (Exception ex)
            {
                result.setException(ex);
            }
            return result;
        }


        //执行检测PLC当前连接状态
        public void doCheckMQTTState()
        {
            plcState = new plcConnectionState();
            while (switch_thread_CheckPLCState)
            {
                try
                {
                    plcState.connectionState = enum_PlcConnectionState.Connected;
                    Thread.Sleep(10000);
                }
                catch (Exception ex)
                {

                }
            }
        }

        public IFuncResult<string> doConnectPLC()
        {
            var result = new FuncResult<string>();
            try
            {
                result.bSuccess = true;
            }
            catch (Exception ex)
            {
                result.setException(ex);
            }
            return result;
        }
        /// <summary>
        /// 执行读数据
        /// </summary>
        public void doReadProduceData(string strSeed, IDataModel model_data)
        {
            try
            {
                var model = model_data as tb_WorkPieceDataCapture_Realtime;
                if (model == null && 1 == 1)
                {
                    Random random = new Random();
                    // 定义两个小数
                    double minValue = 1.0;
                    double maxValue = 10000.0;
                    model = new tb_WorkPieceDataCapture_Realtime()
                    {
                        //总成
                        ZC_Result = (DateTime.Now.Second % 2 == 0 ? "OK" : "NG"),
                        ZC_LaserNo = (random.NextDouble() * (maxValue - minValue) + minValue).ToString(),
                        ZC_SerialNo = (random.NextDouble() * (maxValue - minValue) + minValue).ToString(),
                        ZC_Time = DateTime.Now.ToString("yyyyMMddHHmmss"),
                        ZC_Model = (random.NextDouble() * (maxValue - minValue) + minValue).ToString(),
                        //主体
                        FLJ_ZT_Material = (random.NextDouble() * (maxValue - minValue) + minValue).ToString(),
                        FLJ_ZT_Model = (random.NextDouble() * (maxValue - minValue) + minValue).ToString(),
                        FLJ_ZT_Result = "OK",
                        FLJ_ZT_Value = (random.NextDouble() * (maxValue - minValue) + minValue),
                        FLJ_ZT_Lower = (random.NextDouble() * (maxValue - minValue) + minValue),
                        FLJ_ZT_Upper = (random.NextDouble() * (maxValue - minValue) + minValue),
                        //一次圈
                        FLJ_YCQ_Result = "OK",
                        FLJ_YCQ_Value = (random.NextDouble() * (maxValue - minValue) + minValue),
                        FLJ_YCQ_Material = (random.NextDouble() * (maxValue - minValue) + minValue).ToString(),
                        FLJ_YCQ_Model = (random.NextDouble() * (maxValue - minValue) + minValue).ToString(),
                        FLJ_YCQ_Lower = (random.NextDouble() * (maxValue - minValue) + minValue),
                        FLJ_YCQ_Upper = (random.NextDouble() * (maxValue - minValue) + minValue),
                        //二次圈
                        FLJ_ECQ_Result = "OK",
                        FLJ_ECQ_Value = (random.NextDouble() * (maxValue - minValue) + minValue),
                        FLJ_ECQ_Material = (random.NextDouble() * (maxValue - minValue) + minValue).ToString(),
                        FLJ_ECQ_Model = (random.NextDouble() * (maxValue - minValue) + minValue).ToString(),
                        FLJ_ECQ_Lower = (random.NextDouble() * (maxValue - minValue) + minValue),
                        FLJ_ECQ_Upper = (random.NextDouble() * (maxValue - minValue) + minValue),
                        //隔离环
                        FLJ_GLH_Result = "OK",
                        FLJ_GLH_Value = (random.NextDouble() * (maxValue - minValue) + minValue),
                        FLJ_GLH_Material = (random.NextDouble() * (maxValue - minValue) + minValue).ToString(),
                        FLJ_GLH_Model = (random.NextDouble() * (maxValue - minValue) + minValue).ToString(),
                        FLJ_GLH_Lower = (random.NextDouble() * (maxValue - minValue) + minValue),
                        FLJ_GLH_Upper = (random.NextDouble() * (maxValue - minValue) + minValue),
                        //安全环
                        FLJ_KZH_Result = "OK",
                        FLJ_KZH_Value = (random.NextDouble() * (maxValue - minValue) + minValue),
                        FLJ_KZH_Material = (random.NextDouble() * (maxValue - minValue) + minValue).ToString(),
                        FLJ_KZH_Model = (random.NextDouble() * (maxValue - minValue) + minValue).ToString(),
                        FLJ_KZH_Lower = (random.NextDouble() * (maxValue - minValue) + minValue),
                        FLJ_KZH_Upper = (random.NextDouble() * (maxValue - minValue) + minValue),
                        //拉环
                        FLJ_LH_Result = "OK",
                        FLJ_LH_LaserNo = (random.NextDouble() * (maxValue - minValue) + minValue).ToString(),
                        FLJ_LH_Material = (random.NextDouble() * (maxValue - minValue) + minValue).ToString(),
                        FLJ_LH_Model = (random.NextDouble() * (maxValue - minValue) + minValue).ToString(),
                        FLJ_LH_Photo1 = (random.NextDouble() * (maxValue - minValue) + minValue).ToString(),
                        FLJ_LH_Photo2 = (random.NextDouble() * (maxValue - minValue) + minValue).ToString(),
                        //锁扣
                        FLJ_SK_Result = "OK",
                        FLJ_SK_Material = (random.NextDouble() * (maxValue - minValue) + minValue).ToString(),
                        FLJ_SK_Model = (random.NextDouble() * (maxValue - minValue) + minValue).ToString(),
                        FLJ_SK_Photo = (random.NextDouble() * (maxValue - minValue) + minValue).ToString(),
                        //内密气密
                        JCY_QM_Program = 12,
                        JCY_QM_Result = "OK",
                        JCY_QM_Leak = (random.NextDouble() * (maxValue - minValue) + minValue),
                        JCY_QM_Pressure = (random.NextDouble() * (maxValue - minValue) + minValue),
                        //内密
                        JCY_NM_Result = "OK",
                        JCY_NM_Photo1 = (random.NextDouble() * (maxValue - minValue) + minValue).ToString(),
                        JCY_NM_Photo2 = (random.NextDouble() * (maxValue - minValue) + minValue).ToString(),
                        //外密
                        FLJ_WM_Result = "OK",
                        FLJ_WM_Photo1 = (random.NextDouble() * (maxValue - minValue) + minValue).ToString(),
                        FLJ_WM_Photo2 = (random.NextDouble() * (maxValue - minValue) + minValue).ToString(),
                        FLJ_WM_Material = (random.NextDouble() * (maxValue - minValue) + minValue).ToString(),
                        FLJ_WM_Model = "1",
                        //壳体气密
                        JCY_KTQM_Program = 1,
                        JCY_KTQM_Result = "OK",
                        JCY_KTQM_Leak = (random.NextDouble() * (maxValue - minValue) + minValue),
                        JCY_KTQM_Pressure = (random.NextDouble() * (maxValue - minValue) + minValue),

                        dDate = DateTime.Now
                    };
                    model.FLJ_ECQ_Result = model.FLJ_ECQ_Value > 9800 ? "NG" : "OK";
                    model.FLJ_GLH_Result = model.FLJ_GLH_Value > 9800 ? "NG" : "OK";
                    model.FLJ_KZH_Result = model.FLJ_KZH_Value > 9800 ? "NG" : "OK";
                    model.FLJ_LH_Result = double.Parse(model.FLJ_LH_LaserNo) > 9800 ? "NG" : "OK";
                    model.FLJ_SK_Result = double.Parse(model.FLJ_SK_Model) > 9800 ? "NG" : "OK";
                    model.JCY_QM_Result = model.JCY_QM_Leak > 9800 ? "NG" : "OK";
                    model.FLJ_ECQ_Result = model.FLJ_ECQ_Value > 9800 ? "NG" : "OK";
                    model.JCY_NM_Result = double.Parse(model.JCY_NM_Photo1) > 9800 ? "NG" : "OK";
                    model.FLJ_WM_Result = double.Parse(model.FLJ_WM_Photo1) > 9800 ? "NG" : "OK";
                    model.FLJ_YCQ_Result = model.FLJ_YCQ_Value > 9800 ? "NG" : "OK";
                    model.FLJ_ZT_Result = model.FLJ_ZT_Value > 9800 ? "NG" : "OK";
                    model.ZC_Result = model.FLJ_ECQ_Result == "OK" &&
                                                 model.FLJ_GLH_Result == "OK" &&
                                                 model.FLJ_KZH_Result == "OK" &&
                                                 model.FLJ_LH_Result == "OK" &&
                                                 model.FLJ_SK_Result == "OK" &&
                                                 model.JCY_QM_Result == "OK" &&
                                                 model.FLJ_ECQ_Result == "OK" &&
                                                 model.JCY_NM_Result == "OK" &&
                                                 model.FLJ_WM_Result == "OK" &&
                                                 model.FLJ_YCQ_Result == "OK" &&
                                                 model.FLJ_ZT_Result == "OK" ? "OK" : "NG";
                }
                //string aaa = JsonConvert.SerializeObject(model);
                #region 对OK和NG的特殊处理
                switch (model.ZC_Result ?? "")
                {
                    case "1":
                        model.ZC_Result = "OK";
                        break;
                    case "2":
                        model.ZC_Result = "NG";
                        break;
                    case "0":
                        model.ZC_Result = "";
                        break;
                    default:
                        model.ZC_Result = "NA";
                        break;
                }
                switch (model.FLJ_ZT_Result ?? "")
                {
                    case "1":
                        model.FLJ_ZT_Result = "OK";
                        break;
                    case "2":
                        model.FLJ_ZT_Result = "NG";
                        break;
                    case "0":
                        model.FLJ_ZT_Result = "";
                        break;
                    default:
                        model.FLJ_ZT_Result = "NA";
                        break;
                }
                switch (model.FLJ_YCQ_Result ?? "")
                {
                    case "1":
                        model.FLJ_YCQ_Result = "OK";
                        break;
                    case "2":
                        model.FLJ_YCQ_Result = "NG";
                        break;
                    case "0":
                        model.FLJ_YCQ_Result = "";
                        break;
                    default:
                        model.FLJ_YCQ_Result = "NA";
                        break;
                }
                switch (model.FLJ_GLH_Result ?? "")
                {
                    case "1":
                        model.FLJ_GLH_Result = "OK";
                        break;
                    case "2":
                        model.FLJ_GLH_Result = "NG";
                        break;
                    case "0":
                        model.FLJ_GLH_Result = "";
                        break;
                    default:
                        model.FLJ_GLH_Result = "NA";
                        break;
                }
                switch (model.FLJ_ECQ_Result ?? "")
                {
                    case "1":
                        model.FLJ_ECQ_Result = "OK";
                        break;
                    case "2":
                        model.FLJ_ECQ_Result = "NG";
                        break;
                    case "0":
                        model.FLJ_ECQ_Result = "";
                        break;
                    default:
                        model.FLJ_ECQ_Result = "NA";
                        break;
                }
                switch (model.FLJ_KZH_Result ?? "")
                {
                    case "1":
                        model.FLJ_KZH_Result = "OK";
                        break;
                    case "2":
                        model.FLJ_KZH_Result = "NG";
                        break;
                    case "0":
                        model.FLJ_KZH_Result = "";
                        break;
                    default:
                        model.FLJ_KZH_Result = "NA";
                        break;
                }
                switch (model.FLJ_KZH_PressureResult ?? "")
                {
                    case "1":
                        model.FLJ_KZH_PressureResult = "OK";
                        break;
                    case "2":
                        model.FLJ_KZH_PressureResult = "NG";
                        break;
                    case "0":
                        model.FLJ_KZH_PressureResult = "";
                        break;
                    default:
                        model.FLJ_KZH_PressureResult = "NA";
                        break;
                }
                switch (model.FLJ_SK_Result ?? "")
                {
                    case "1":
                        model.FLJ_SK_Result = "OK";
                        break;
                    case "2":
                        model.FLJ_SK_Result = "NG";
                        break;
                    case "0":
                        model.FLJ_SK_Result = "";
                        break;
                    default:
                        model.FLJ_SK_Result = "NA";
                        break;
                }
                switch (model.JCY_NM_Result ?? "")
                {
                    case "1":
                        model.JCY_NM_Result = "OK";
                        break;
                    case "2":
                        model.JCY_NM_Result = "NG";
                        break;
                    case "0":
                        model.JCY_NM_Result = "";
                        break;
                    default:
                        model.JCY_NM_Result = "NA";
                        break;
                }
                switch (model.FLJ_WM_Result ?? "")
                {
                    case "1":
                        model.FLJ_WM_Result = "OK";
                        break;
                    case "2":
                        model.FLJ_WM_Result = "NG";
                        break;
                    case "0":
                        model.FLJ_WM_Result = "";
                        break;
                    default:
                        model.FLJ_WM_Result = "NA";
                        break;
                }
                switch (model.JCY_QM_Result ?? "")
                {
                    case "1":
                        model.JCY_QM_Result = "OK";
                        break;
                    case "2":
                        model.JCY_QM_Result = "NG";
                        break;
                    case "0":
                        model.JCY_QM_Result = "";
                        break;
                    default:
                        model.JCY_QM_Result = "NA";
                        break;
                }
                model.dDate = DateTime.Now;
                string strjson = JsonConvert.SerializeObject(model);
                #endregion
                lock (this.cache_DataCapture)
                {
                    this.cache_DataCapture.Add(model);
                    log.Info("(" + strSeed + ")成功将实体类加入待存储缓存");
                }
            }
            catch (Exception ex)
            {
                log.Error("(" + strSeed + ")将实体类加入待存储缓存出错！" + ex.ToString());
            }
        }
        /// <summary>
        /// 执行保存数据
        /// </summary>
        public void doSaveData()
        {
            while (switch_thread_SaveData)
            {
                try
                {
                    if (this.cache_DataCapture != null && this.cache_DataCapture.Count > 0)
                    {
                        var model = this.cache_DataCapture[0];
                        var result = this.respository.saveData(model);
                        bool dataSaved = false;
                        MQTT_Message msg = new MQTT_Message();
                        msg.Topic = "12.61S生产数据存储结果";
                        if (result.bSuccess)    //成功写入了数据库
                        {
                            log.Info("(model.ZC_SerialNo为" + model.ZC_SerialNo + ")的数据存储成功");
                            msg.text = "{\"ZC_SerialNo\":\"" + model.ZC_SerialNo + "\",\"result\":\"1\",\"msg\":\"\"}";
                            dataSaved = true;
                        }
                        else    //写入数据库失败时往文件里写
                        {
                            try
                            {
                                string strDataJson = JsonConvert.SerializeObject(model);
                                File.AppendAllLines("Files\\UnsavedData", new List<string>() { strDataJson });
                                log.Error("(model.iID为" + model.ZC_SerialNo + ")的数据存储到数据库失败，已写入文件中待人工处理");
                                msg.text = "{\"ZC_SerialNo\":\"" + model.ZC_SerialNo + "\",\"result\":\"1\",\"msg\":\"数据存储到数据库失败(" + result.strMsg + ")，已写入文件中待人工处理\"}";
                                dataSaved = true;
                            }
                            catch (Exception ex)
                            {
                                log.Error("(model.ZC_SerialNo为" + model.ZC_SerialNo + ")的数据写入文件中待人工处理失败，需根据iID排查日志中的源报文进行处置");
                                msg.text = "{\"ZC_SerialNo\":\"" + model.ZC_SerialNo + "\",\"result\":\"0\",\"msg\":\"数据存储到数据库与文件均失败\"}";
                                dataSaved = false;
                            }
                        }
                        this.MQTTPublish(msg);
                        this.cache_DataCapture.RemoveAt(0);

                        if (this.evt_FeedingNewestData != null && dataSaved) this.evt_FeedingNewestData(model);  //向调用者反馈最新的生产数据
                    }
                    Thread.Sleep(300);
                }
                catch (Exception ex)
                {
                    log.Error("存储数据出现未捕获的异常！" + ex.ToString());
                }
            }
        }

        private void MQTT_MessageReceived(MQTT_Message msg)
        {
            switch (msg.Topic)
            {
                case "12.61S生产数据":
                    MQTT_Message msg_feedback = new MQTT_Message();
                    msg_feedback.Topic = "12.61S生产数据存储结果";
                    string strSeed = Guid.NewGuid().ToString();
                    try
                    {
                        log.Info("(" + strSeed + ")接收到生产数据:" + msg.text);
                        #region 对信息的特殊处理
                        msg.text = msg.text.Replace("\\u0000", "");
                        if (msg.text.Contains('\\'))
                        {
                            if (msg.text.IndexOf('\\') > -1 && msg.text.IndexOf("\\\\") < 0)
                            {
                                //如果报文中出现单个的反斜杠，则转换成双反斜杠，否则报错
                                msg.text = msg.text.Replace("\\", "\\\\");
                            }
                            else
                            {
                                log.Error("(" + strSeed + ")处理反斜杠失败，报文中即有单个'\\'字符又有连续的'\\'字符");
                                msg_feedback.text = "{\"ZC_SerialNo\":\"\",\"result\":\"0\",\"msg\":\"处理反斜杠失败，报文中即有单个'\\'字符又有连续的'\\'字符\"}";
                                this.MQTTPublish(msg_feedback);
                                return;
                            }
                        }
                        log.Info("(" + strSeed + ")接收到生产数据经过转换后是:" + msg.text);
                        #endregion

                        // 尝试将消息转换为请求对象
                        WorkPieceDataCapture_Request? request = null;
                        try
                        {
                            request = JsonConvert.DeserializeObject<WorkPieceDataCapture_Request>(msg.text);
                        }
                        catch (Exception ex)
                        {
                            log.Error("(" + strSeed + ")无法将生产数据解析成请求对象: " + ex.Message);
                            msg_feedback.text = "{\"ZC_SerialNo\":\"\",\"result\":\"0\",\"msg\":\"无法将生产数据解析成请求对象: " + ex.Message + "\"}";
                            this.MQTTPublish(msg_feedback);
                            return;
                        }

                        if (request == null)
                        {
                            log.Error("(" + strSeed + ")解析成实体类出错,解析结果为空");
                            msg_feedback.text = "{\"ZC_SerialNo\":\"\",\"result\":\"0\",\"msg\":\"解析成实体类出错,解析结果为空\"}";
                            this.MQTTPublish(msg_feedback);
                            return;
                        }

                        DateTime dateTime = DateTime.Now;

                        // 对数据进行验证
                        if (string.IsNullOrEmpty(request.ZC_SerialNo))
                        {
                            log.Error("(" + strSeed + ")解析成实体类出错,总成序列号为空");
                            msg_feedback.text = "{\"ZC_SerialNo\":\"\",\"result\":\"0\",\"msg\":\"解析成实体类出错,总成序列号为空\"}";
                            this.MQTTPublish(msg_feedback);
                            return;
                        }
                        var result_SerialNo = respository.getBySerialNoFromRealTime(request.ZC_SerialNo); //总成标识码不能重复
                        if (result_SerialNo.bSuccess)
                        {
                            if (result_SerialNo.obj_Return != null)
                            {
                                log.Info("(" + strSeed + ")在库中找到相同的总成标识码，此为重复数据，不做任何处理");
                                msg_feedback.text = "{\"ZC_SerialNo\":\"" + request.ZC_SerialNo + "\",\"result\":0,\"msg\":\"在库中找到相同的总成标识码，此为重复数据，不做任何处理\"}";
                                this.MQTTPublish(msg_feedback);
                                break;
                            }
                        }
                        else
                        {
                            log.Info("(" + strSeed + ")校验数据是否重复出错!" + result_SerialNo.strMsg);
                            msg_feedback.text = "{\"ZC_SerialNo\":\"" + request.ZC_SerialNo + "\",\"result\":0,\"msg\":\"校验数据是否重复出错!" + result_SerialNo.strMsg + "\"}";
                            this.MQTTPublish(msg_feedback);
                            break;
                        }
                        if (!DateTime.TryParseExact(request.ZC_Time, "yyyyMMddHHmmss", new CultureInfo("en-US"), DateTimeStyles.None, out dateTime))
                        {
                            log.Error("(" + strSeed + ")解析成实体类出错,无法将ZC_Time的值" + request.ZC_Time + "转换成日期时间");
                            msg_feedback.text = "{\"ZC_SerialNo\":\"" + request.ZC_SerialNo + "\",\"result\":\"0\",\"msg\":\"解析成实体类出错,无法将ZC_Time的值" + request.ZC_Time + "转换成日期时间\"}";
                            this.MQTTPublish(msg_feedback);
                            return;
                        }
                        #region 激光打标码的校验
                        if (request.CheckLaserNo == true)
                        {
                            if (string.IsNullOrEmpty(request.ZC_LaserNo))     //总成激光码不能为空
                            {
                                log.Error("(" + strSeed + ")总成激光码不能为空");
                                msg_feedback.text = "{\"ZC_SerialNo\":\"" + request.ZC_SerialNo + "\",\"result\":0,\"msg\":\"总成激光码不能为空\"}";
                                this.MQTTPublish(msg_feedback);
                                break;
                            }
                            var result_LaserNo = respository.getByLaserNoFromRealTime(request.ZC_LaserNo); //激光码不能重复
                            if (result_LaserNo.bSuccess)
                            {
                                if (result_LaserNo.obj_Return != null)
                                {
                                    log.Info("(" + strSeed + ")在库中找到相同的激光码，此为重复数据，不做任何处理");
                                    msg_feedback.text = "{\"ZC_SerialNo\":\"" + request.ZC_SerialNo + "\",\"result\":0,\"msg\":\"在库中找到相同的激光码，此为重复数据，不做任何处理\"}";
                                    this.MQTTPublish(msg_feedback);
                                    break;
                                }
                            }
                            else
                            {
                                log.Info("(" + strSeed + ")校验数据是否重复出错!" + result_LaserNo.strMsg);
                                msg_feedback.text = "{\"ZC_SerialNo\":\"" + request.ZC_SerialNo + "\",\"result\":0,\"msg\":\"校验数据是否重复出错!" + result_LaserNo.strMsg + "\"}";
                                this.MQTTPublish(msg_feedback);
                                break;
                            }
                        }

                        #endregion
                        // 将请求对象转换为实体对象
                        tb_WorkPieceDataCapture_Realtime model = new tb_WorkPieceDataCapture_Realtime
                        {
                            iID = null,
                            ZC_SerialNo = request.ZC_SerialNo,
                            ZC_Model = request.ZC_Model,
                            ZC_Time = request.ZC_Time,
                            ZC_LaserNo = request.ZC_LaserNo,
                            ZC_Result = request.ZC_Result,
                            FLJ_ZT_Model = request.FLJ_ZT_Model,
                            FLJ_ZT_Material = request.FLJ_ZT_Material,
                            FLJ_ZT_Result = request.FLJ_ZT_Result,
                            FLJ_ZT_Value = request.FLJ_ZT_Value,
                            FLJ_ZT_Upper = request.FLJ_ZT_Upper,
                            FLJ_ZT_Lower = request.FLJ_ZT_Lower,
                            FLJ_YCQ_Model = request.FLJ_YCQ_Model,
                            FLJ_YCQ_Material = request.FLJ_YCQ_Material,
                            FLJ_YCQ_Result = request.FLJ_YCQ_Result,
                            FLJ_YCQ_Value = request.FLJ_YCQ_Value,
                            FLJ_YCQ_Upper = request.FLJ_YCQ_Upper,
                            FLJ_YCQ_Lower = request.FLJ_YCQ_Lower,
                            FLJ_GLH_Model = request.FLJ_GLH_Model,
                            FLJ_GLH_Material = request.FLJ_GLH_Material,
                            FLJ_GLH_Result = request.FLJ_GLH_Result,
                            FLJ_GLH_Value = request.FLJ_GLH_Value,
                            FLJ_GLH_Upper = request.FLJ_GLH_Upper,
                            FLJ_GLH_Lower = request.FLJ_GLH_Lower,
                            FLJ_ECQ_Model = request.FLJ_ECQ_Model,
                            FLJ_ECQ_Material = request.FLJ_ECQ_Material,
                            FLJ_ECQ_Result = request.FLJ_ECQ_Result,
                            FLJ_ECQ_Value = request.FLJ_ECQ_Value,
                            FLJ_ECQ_Upper = request.FLJ_ECQ_Upper,
                            FLJ_ECQ_Lower = request.FLJ_ECQ_Lower,
                            FLJ_KZH_Model = request.FLJ_KZH_Model,
                            FLJ_KZH_Material = request.FLJ_KZH_Material,
                            FLJ_KZH_Result = request.FLJ_KZH_Result,
                            FLJ_KZH_Value = request.FLJ_KZH_Value,
                            FLJ_KZH_Upper = request.FLJ_KZH_Upper,
                            FLJ_KZH_Lower = request.FLJ_KZH_Lower,
                            FLJ_KZH_PressureResult = request.FLJ_KZH_PressureResult,
                            FLJ_WM_Model = request.FLJ_WM_Model,
                            FLJ_WM_Material = request.FLJ_WM_Material,
                            FLJ_WM_Result = request.FLJ_WM_Result,
                            FLJ_WM_Photo1 = request.FLJ_WM_Photo1,
                            FLJ_WM_Photo2 = request.FLJ_WM_Photo2,
                            FLJ_SK_Model = request.FLJ_SK_Model,
                            FLJ_SK_Material = request.FLJ_SK_Material,
                            FLJ_SK_Result = request.FLJ_SK_Result,
                            FLJ_SK_Photo = request.FLJ_SK_Photo,
                            JCY_QM_Result = request.JCY_QM_Result,
                            JCY_QM_Program = request.JCY_QM_Program,
                            JCY_QM_Pressure = request.JCY_QM_Pressure,
                            JCY_QM_Leak = request.JCY_QM_Leak,
                            JCY_NM_Photo1 = request.JCY_NM_Photo1,
                            JCY_NM_Photo2 = request.JCY_NM_Photo2,
                            JCY_NM_Result = request.JCY_NM_Result,
                            JCY_KTQM_Result = request.JCY_KTQM_Result,
                            JCY_KTQM_Program = request.JCY_KTQM_Program,
                            JCY_KTQM_Pressure = request.JCY_KTQM_Pressure,
                            JCY_KTQM_Leak = request.JCY_KTQM_Leak,
                            FLJ_LH_Model = request.FLJ_LH_Model,
                            FLJ_LH_Material = request.FLJ_LH_Material,
                            FLJ_LH_Result = request.FLJ_LH_Result,
                            FLJ_LH_LaserNo = request.FLJ_LH_LaserNo,
                            FLJ_LH_Photo1 = request.FLJ_LH_Photo1,
                            FLJ_LH_Photo2 = request.FLJ_LH_Photo2,
                            dDate = dateTime
                        };

                        var list_coordinate = new List<CoordinatePoint>();
                        Type entityType = typeof(WorkPieceDataCapture_Request);
                        for (int i = 1; i < 101; i++)
                        {
                            string propX = "YLQX_X" + i;
                            string propY = "YLQX_Y" + i;
                            PropertyInfo propertyX = entityType.GetProperty(propX);
                            PropertyInfo propertyY = entityType.GetProperty(propY);
                            if (propertyX != null && propertyY != null)
                            {
                                object valueX = propertyX.GetValue(request);
                                object valueY = propertyY.GetValue(request);
                                if (valueX != null && valueY != null)
                                {
                                    double x = Convert.ToDouble(valueX);
                                    double y = Convert.ToDouble(valueY);
                                    list_coordinate.Add(new CoordinatePoint() { X = x, Y = y });
                                }
                            }
                        }

                        // 如果有坐标数据，则转换为JSON字符串
                        model.CoordinateData = JsonConvert.SerializeObject(list_coordinate);
                        log.Info("(" + strSeed + ")坐标数据已保存到CoordinateData字段");

                        // 对数据进行过滤或保存
                        if (model.ZC_SerialNo == strLastSerialNo)
                        {
                            log.Info("(" + strSeed + ")此为重复数据，不做任何处理");
                            msg_feedback.text = "{\"ZC_SerialNo\":\"" + model.ZC_SerialNo + "\",\"result\":\"0\",\"msg\":\"此为重复数据，不做任何处理\"}";
                            this.MQTTPublish(msg_feedback);
                        }
                        else
                        {
                            strLastSerialNo = model.ZC_SerialNo;
                            log.Info("(" + strSeed + ")成功解析成实体类(model.ZC_SerialNo:" + model.ZC_SerialNo + ")，将进行数据保存");
                            this.doReadProduceData(strSeed, model);
                        }
                    }
                    catch (Exception ex)
                    {
                        log.Error("(" + strSeed + ")数据处理出错！" + ex.ToString());
                        msg_feedback.text = "{\"ZC_SerialNo\":\"\",\"result\":\"0\",\"msg\":\"数据处理出错！" + ex.Message + "\"}";
                        this.MQTTPublish(msg_feedback);
                    }
                    break;
                default:
                    break;
            }
        }
        /// <summary>
        /// 发布MQTT信息
        /// </summary>
        /// <param name="msg"></param>
        private void MQTTPublish(MQTT_Message msg)
        {
            for (int i = 0; i < 10; i++)
            {
                var result_mqtt = mqtt.ClientPublish(msg);
                if (result_mqtt.bSuccess) { break; }
            }
        }
    }
}
