﻿<UserControl x:Class="WorkPieceDataCapture.Views.uc_ProduceDetail"  xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
  xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"  xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
  xmlns:d="http://schemas.microsoft.com/expression/blend/2008" mc:Ignorable="d" d:DesignHeight="612" d:DesignWidth="1200">
    <Grid>
        <WrapPanel Grid.Row="3" Grid.Column="2" Background="#015478">
            <Label Content="总成ddd" Style="{DynamicResource DataDetailTitle}" Height="60"></Label>
            <Border Style="{DynamicResource DataDetailContentBorder}" Width="1032" Height="60">
                <Grid Height="60" Background="Transparent">
                    <Grid.RowDefinitions>
                        <RowDefinition></RowDefinition>
                        <RowDefinition></RowDefinition>
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"></ColumnDefinition>
                        <ColumnDefinition Width="250"></ColumnDefinition>
                    </Grid.ColumnDefinitions>
                    <WrapPanel Grid.Row="0">
                        <Label Content="标识码：" Style="{DynamicResource DataDetailFieldTitle_5Words}" ></Label>
                        <Label Content="{Binding dataModel_Search.ZC_SerialNo}" Style="{DynamicResource DataDetailFieldValue}"></Label>
                        <Label Content="生产日期：" Style="{DynamicResource DataDetailFieldTitle_5Words}"></Label>
                        <Label Content="{Binding dataModel_Search.ZC_Time,StringFormat=yyyy-MM-dd}" Style="{DynamicResource DataDetailFieldValue}" ></Label>
                    </WrapPanel>
                    <WrapPanel Grid.Row="1">
                        <Label Content="总成型号：" Style="{DynamicResource DataDetailFieldTitle_5Words}"></Label>
                        <Label Content="{Binding dataModel_Search.ZC_Model}" Style="{DynamicResource DataDetailFieldValue}"></Label>
                        <Label Content="激光打标码：" Style="{DynamicResource DataDetailFieldTitle_5Words}"></Label>
                        <Label Content="{Binding dataModel_Search.ZC_LaserNo}" Style="{DynamicResource DataDetailFieldValue}"></Label>
                    </WrapPanel>
                    <Label Content="综合检测结果：" Style="{DynamicResource DataDetailFieldTitle_6Words}" FontWeight="Bold" Grid.Column="1" Grid.RowSpan="2" HorizontalAlignment="Left"></Label>
                    <TextBlock Grid.Column="1" Grid.RowSpan="2" Text="{Binding dataModel_Search.ZC_Result}"   FontSize="30" FontFamily="Arial" Height="50" Width="60" Margin="150,13,5,0" FontWeight="Bold">
                        <TextBlock.Style>
                            <Style TargetType="{x:Type TextBlock}">
                                <Style.Triggers>
                                    <DataTrigger Binding="{Binding dataModel_Search.ZC_Result}" Value="OK">
                                        <Setter Property="Foreground" Value="#00FFFF" />
                                    </DataTrigger>
                                    <DataTrigger Binding="{Binding dataModel_Search.ZC_Result}" Value="NG">
                                        <Setter Property="Foreground" Value="Red" />
                                    </DataTrigger>
                                    <DataTrigger Binding="{Binding dataModel_Search.ZC_Result}" Value="NA">
                                        <Setter Property="Foreground" Value="Gray" />
                                    </DataTrigger>
                                    <!-- 其他触发器 -->
                                </Style.Triggers>
                            </Style>
                        </TextBlock.Style>
                <Run Text="{Binding dataModel_Search.ZC_Result}" />
                    </TextBlock>
                </Grid>
            </Border>
            <Label Content="分零件：主体" Style="{DynamicResource DataDetailTitle}" Height="60"></Label>
            <Border Style="{DynamicResource DataDetailContentBorder}" Width="1032" Height="60">
                <Grid Height="60" Background="Transparent">
                    <Grid.RowDefinitions>
                        <RowDefinition></RowDefinition>
                        <RowDefinition></RowDefinition>
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"></ColumnDefinition>
                        <ColumnDefinition Width="250"></ColumnDefinition>
                    </Grid.ColumnDefinitions>
                    <WrapPanel Grid.Row="0" Grid.ColumnSpan="2">
                        <Label Content="型号码：" Style="{DynamicResource DataDetailFieldTitle_5Words}" ></Label>
                        <Label Content="{Binding dataModel_Search.FLJ_ZT_Model}" Style="{DynamicResource DataDetailFieldValue}"></Label>
                        <Label Content="物料编码：" Style="{DynamicResource DataDetailFieldTitle_5Words}"></Label>
                        <Label Content="{Binding dataModel_Search.FLJ_ZT_Material}" Style="{DynamicResource DataDetailFieldValue}" ></Label>
                    </WrapPanel>
                    <WrapPanel Grid.Row="1" Grid.ColumnSpan="2">
                        <Label Content="位移检测值：" Style="{DynamicResource DataDetailFieldTitle_5Words}"></Label>
                        <Label Content="{Binding dataModel_Search.FLJ_ZT_Value}" Style="{DynamicResource DataDetailFieldValue_Tiny}"></Label>
                        <Label Content="上限：" Style="{DynamicResource DataDetailFieldTitle_4Words}" Margin="25,0,0,0"></Label>
                        <Label Content="{Binding dataModel_Search.FLJ_ZT_Upper}" Grid.Row="1" Style="{DynamicResource DataDetailFieldValue_Tiny}"></Label>
                        <Label Content="下限：" Style="{DynamicResource DataDetailFieldTitle_4Words}" Margin="25,0,0,0"></Label>
                        <Label Content="{Binding dataModel_Search.FLJ_ZT_Lower}" Grid.Row="1" Style="{DynamicResource DataDetailFieldValue_Tiny}"></Label>
                    </WrapPanel>
                    <WrapPanel Grid.Column="1" Grid.RowSpan="2" VerticalAlignment="Center">
                        <Label Content="异物位移检测结果：" Style="{DynamicResource DataDetailFieldTitle_6Words}" FontWeight="Bold" ></Label>
                        <TextBlock FontWeight="Bold" FontSize="22" HorizontalAlignment="Center" VerticalAlignment="Center" Width="65" TextAlignment="Center">
                            <TextBlock.Style>
                                <Style TargetType="{x:Type TextBlock}">
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding dataModel_Search.FLJ_ZT_Result}" Value="OK">
                                            <Setter Property="Foreground" Value="#00FFFF" />
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding dataModel_Search.FLJ_ZT_Result}" Value="NG">
                                            <Setter Property="Foreground" Value="Red" />
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding dataModel_Search.FLJ_ZT_Result}" Value="NA">
                                            <Setter Property="Foreground" Value="Gray" />
                                        </DataTrigger>
                                        <!-- 其他触发器 -->
                                    </Style.Triggers>
                                </Style>
                            </TextBlock.Style>
                            <Run Text="{Binding dataModel_Search.FLJ_ZT_Result}" />
                        </TextBlock>
                    </WrapPanel>
                </Grid>
            </Border>
            <Label Content="分零件：一次圈" Style="{DynamicResource DataDetailTitle}" Height="30"></Label>
            <Border Style="{DynamicResource DataDetailContentBorder}" Width="1032" Height="30">
                <Grid Height="30" Background="Transparent">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="30"></RowDefinition>
                        <RowDefinition Height="0"></RowDefinition>
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"></ColumnDefinition>
                        <ColumnDefinition Width="200"></ColumnDefinition>
                    </Grid.ColumnDefinitions>
                    <WrapPanel Grid.Row="0" Grid.ColumnSpan="2">
                        <Label Content="型号码：" Style="{DynamicResource DataDetailFieldTitle_5Words}" ></Label>
                        <Label Content="{Binding dataModel_Search.FLJ_YCQ_Model}" Style="{DynamicResource DataDetailFieldValue}"></Label>
                        <Label Content="物料编码：" Style="{DynamicResource DataDetailFieldTitle_5Words}"></Label>
                        <Label Content="{Binding dataModel_Search.FLJ_YCQ_Material}" Style="{DynamicResource DataDetailFieldValue}" ></Label>
                    </WrapPanel>
                    <WrapPanel Grid.Row="1" Grid.ColumnSpan="2">
                        <Label Content="位移检测值：" Style="{DynamicResource DataDetailFieldTitle_5Words}" Visibility="Collapsed"></Label>
                        <Label Content="{Binding dataModel_Search.FLJ_YCQ_Value}" Style="{DynamicResource DataDetailFieldValue_Tiny}" Visibility="Collapsed"></Label>
                        <Label Content="上限：" Style="{DynamicResource DataDetailFieldTitle_4Words}"  Margin="25,0,0,0" Visibility="Collapsed"></Label>
                        <Label Content="{Binding dataModel_Search.FLJ_YCQ_Upper}" Grid.Row="1" Style="{DynamicResource DataDetailFieldValue_Tiny}" Visibility="Collapsed"></Label>
                        <Label Content="下限：" Style="{DynamicResource DataDetailFieldTitle_4Words}" Margin="25,0,0,0" Visibility="Collapsed" ></Label>
                        <Label Content="{Binding dataModel_Search.FLJ_YCQ_Lower}" Grid.Row="1" Style="{DynamicResource DataDetailFieldValue_Tiny}" Visibility="Collapsed"></Label>
                        <Label Content="检测结果：" Style="{DynamicResource DataDetailFieldTitle_6Words}" FontWeight="Bold" Margin="204,0,0,0" Visibility="Collapsed"></Label>
                        <TextBlock FontWeight="Bold" FontSize="22" HorizontalAlignment="Center" VerticalAlignment="Center" Width="65" TextAlignment="Center" Visibility="Collapsed">
                            <TextBlock.Style>
                                <Style TargetType="{x:Type TextBlock}">
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding dataModel_Search.FLJ_YCQ_Result}" Value="OK">
                                            <Setter Property="Foreground" Value="#00FFFF" />
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding dataModel_Search.FLJ_YCQ_Result}" Value="NG">
                                            <Setter Property="Foreground" Value="Red" />
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding dataModel_Search.FLJ_YCQ_Result}" Value="NA">
                                            <Setter Property="Foreground" Value="Gray" />
                                        </DataTrigger>
                                        <!-- 其他触发器 -->
                                    </Style.Triggers>
                                </Style>
                            </TextBlock.Style>
                            <Run Text="{Binding dataModel_Search.FLJ_YCQ_Result}" />
                        </TextBlock>
                    </WrapPanel>
                </Grid>
            </Border>
            <Label Content="分零件：隔离环" Style="{DynamicResource DataDetailTitle}" Height="60"></Label>
            <Border Style="{DynamicResource DataDetailContentBorder}" Width="1032" Height="60">
                <Grid Height="60" Background="Transparent">
                    <Grid.RowDefinitions>
                        <RowDefinition></RowDefinition>
                        <RowDefinition></RowDefinition>
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"></ColumnDefinition>
                        <ColumnDefinition Width="250"></ColumnDefinition>
                    </Grid.ColumnDefinitions>
                    <WrapPanel Grid.Row="0" Grid.ColumnSpan="2">
                        <Label Content="型号码：" Style="{DynamicResource DataDetailFieldTitle_5Words}" ></Label>
                        <Label Content="{Binding dataModel_Search.FLJ_GLH_Model}" Style="{DynamicResource DataDetailFieldValue}"></Label>
                        <Label Content="物料编码：" Style="{DynamicResource DataDetailFieldTitle_5Words}"></Label>
                        <Label Content="{Binding dataModel_Search.FLJ_GLH_Material}" Style="{DynamicResource DataDetailFieldValue}" ></Label>
                    </WrapPanel>
                    <WrapPanel Grid.Row="1" Grid.ColumnSpan="2">
                        <Label Content="位移检测值：" Style="{DynamicResource DataDetailFieldTitle_5Words}"></Label>
                        <Label Content="{Binding dataModel_Search.FLJ_GLH_Value}" Style="{DynamicResource DataDetailFieldValue_Tiny}"></Label>
                        <Label Content="上限：" Style="{DynamicResource DataDetailFieldTitle_4Words}"  Margin="25,0,0,0"></Label>
                        <Label Content="{Binding dataModel_Search.FLJ_GLH_Upper}" Grid.Row="1" Style="{DynamicResource DataDetailFieldValue_Tiny}"></Label>
                        <Label Content="下限：" Style="{DynamicResource DataDetailFieldTitle_4Words}"  Margin="25,0,0,0"></Label>
                        <Label Content="{Binding dataModel_Search.FLJ_GLH_Lower}" Grid.Row="1" Style="{DynamicResource DataDetailFieldValue_Tiny}"></Label>
                    </WrapPanel>
                    <WrapPanel Grid.Column="1" Grid.RowSpan="2" VerticalAlignment="Center">
                        <Label Content="检测结果：" Style="{DynamicResource DataDetailFieldTitle_6Words}" FontWeight="Bold" ></Label>
                        <TextBlock FontWeight="Bold" FontSize="22" HorizontalAlignment="Center" VerticalAlignment="Center" Width="65" TextAlignment="Center">
                            <TextBlock.Style>
                                <Style TargetType="{x:Type TextBlock}">
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding dataModel_Search.FLJ_GLH_Result}" Value="OK">
                                            <Setter Property="Foreground" Value="#00FFFF" />
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding dataModel_Search.FLJ_GLH_Result}" Value="NG">
                                            <Setter Property="Foreground" Value="Red" />
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding dataModel_Search.FLJ_GLH_Result}" Value="NA">
                                            <Setter Property="Foreground" Value="Gray" />
                                        </DataTrigger>
                                        <!-- 其他触发器 -->
                                    </Style.Triggers>
                                </Style>
                            </TextBlock.Style>
                            <Run Text="{Binding dataModel_Search.FLJ_GLH_Result}" />
                        </TextBlock>
                    </WrapPanel>
                </Grid>
            </Border>
            <Label Content="分零件：二次圈" Style="{DynamicResource DataDetailTitle}" Height="60"></Label>
            <Border Style="{DynamicResource DataDetailContentBorder}" Width="1032" Height="60">
                <Grid Height="60" Background="Transparent">
                    <Grid.RowDefinitions>
                        <RowDefinition></RowDefinition>
                        <RowDefinition></RowDefinition>
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"></ColumnDefinition>
                        <ColumnDefinition Width="250"></ColumnDefinition>
                    </Grid.ColumnDefinitions>
                    <WrapPanel Grid.Row="0" Grid.ColumnSpan="2">
                        <Label Content="型号码：" Style="{DynamicResource DataDetailFieldTitle_5Words}" ></Label>
                        <Label Content="{Binding dataModel_Search.FLJ_ECQ_Model}" Style="{DynamicResource DataDetailFieldValue}"></Label>
                        <Label Content="物料编码：" Style="{DynamicResource DataDetailFieldTitle_5Words}"></Label>
                        <Label Content="{Binding dataModel_Search.FLJ_ECQ_Material}" Style="{DynamicResource DataDetailFieldValue}" ></Label>
                    </WrapPanel>
                    <WrapPanel Grid.Row="1" Grid.ColumnSpan="2">
                        <Label Content="位移检测值：" Style="{DynamicResource DataDetailFieldTitle_5Words}"></Label>
                        <Label Content="{Binding dataModel_Search.FLJ_ECQ_Value}" Style="{DynamicResource DataDetailFieldValue_Tiny}"></Label>
                        <Label Content="上限：" Style="{DynamicResource DataDetailFieldTitle_4Words}"  Margin="25,0,0,0"></Label>
                        <Label Content="{Binding dataModel_Search.FLJ_ECQ_Upper}" Grid.Row="1" Style="{DynamicResource DataDetailFieldValue_Tiny}"></Label>
                        <Label Content="下限：" Style="{DynamicResource DataDetailFieldTitle_4Words}"  Margin="25,0,0,0"></Label>
                        <Label Content="{Binding dataModel_Search.FLJ_ECQ_Lower}" Grid.Row="1" Style="{DynamicResource DataDetailFieldValue_Tiny}"></Label>
                    </WrapPanel>
                    <WrapPanel Grid.Column="1" Grid.RowSpan="2" VerticalAlignment="Center">
                        <Label Content="检测结果：" Style="{DynamicResource DataDetailFieldTitle_6Words}" FontWeight="Bold"></Label>
                        <TextBlock FontWeight="Bold" FontSize="22" HorizontalAlignment="Center" VerticalAlignment="Center" Width="65" TextAlignment="Center">
                            <TextBlock.Style>
                                <Style TargetType="{x:Type TextBlock}">
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding dataModel_Search.FLJ_ECQ_Result}" Value="OK">
                                            <Setter Property="Foreground" Value="#00FFFF" />
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding dataModel_Search.FLJ_ECQ_Result}" Value="NG">
                                            <Setter Property="Foreground" Value="Red" />
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding dataModel_Search.FLJ_ECQ_Result}" Value="NA">
                                            <Setter Property="Foreground" Value="Gray" />
                                        </DataTrigger>
                                        <!-- 其他触发器 -->
                                    </Style.Triggers>
                                </Style>
                            </TextBlock.Style>
                            <Run Text="{Binding dataModel_Search.FLJ_ECQ_Result}" />
                        </TextBlock>
                    </WrapPanel>
                </Grid>
            </Border>
            <Label Content="分零件：安全环" Style="{DynamicResource DataDetailTitle}" Height="60"></Label>
            <Border Style="{DynamicResource DataDetailContentBorder}" Width="1032" Height="60">
                <Grid Height="60" Background="Transparent">
                    <Grid.RowDefinitions>
                        <RowDefinition></RowDefinition>
                        <RowDefinition></RowDefinition>
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"></ColumnDefinition>
                        <ColumnDefinition Width="250"></ColumnDefinition>
                    </Grid.ColumnDefinitions>
                    <WrapPanel Grid.Row="0" Grid.ColumnSpan="2">
                        <Label Content="型号码：" Style="{DynamicResource DataDetailFieldTitle_5Words}" ></Label>
                        <Label Content="{Binding dataModel_Search.FLJ_KZH_Model}" Style="{DynamicResource DataDetailFieldValue}"></Label>
                        <Label Content="物料编码：" Style="{DynamicResource DataDetailFieldTitle_5Words}"></Label>
                        <Label Content="{Binding dataModel_Search.FLJ_KZH_Material}" Style="{DynamicResource DataDetailFieldValue}" ></Label>
                    </WrapPanel>
                    <WrapPanel Grid.Row="1" Grid.ColumnSpan="2">
                        <Label Content="位移检测值：" Style="{DynamicResource DataDetailFieldTitle_5Words}"></Label>
                        <Label Content="{Binding dataModel_Search.FLJ_KZH_Value}" Style="{DynamicResource DataDetailFieldValue_Tiny}"></Label>
                        <Label Content="上限：" Style="{DynamicResource DataDetailFieldTitle_4Words}"  Margin="25,0,0,0"></Label>
                        <Label Content="{Binding dataModel_Search.FLJ_KZH_Upper}" Grid.Row="1" Style="{DynamicResource DataDetailFieldValue_Tiny}"></Label>
                        <Label Content="下限：" Style="{DynamicResource DataDetailFieldTitle_4Words}"  Margin="25,0,0,0"></Label>
                        <Label Content="{Binding dataModel_Search.FLJ_KZH_Lower}" Grid.Row="1" Style="{DynamicResource DataDetailFieldValue_Tiny}"></Label>
                        <Button Content="压力曲线图" Tag="ylqxt" Style="{DynamicResource FileLinkButton}" Width="85" Height="24" Margin="0,0,0,0" Click="Button_Click"></Button>
                    </WrapPanel>
                    <WrapPanel Grid.Column="1" Grid.RowSpan="2" VerticalAlignment="Center">
                        <Label Content="检测结：" Style="{DynamicResource DataDetailFieldTitle_6Words}" FontWeight="Bold"  ></Label>
                        <TextBlock FontWeight="Bold" FontSize="22" HorizontalAlignment="Center" VerticalAlignment="Center" Width="65" TextAlignment="Center">
                            <TextBlock.Style>
                                <Style TargetType="{x:Type TextBlock}">
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding dataModel_Search.FLJ_KZH_Result}" Value="OK">
                                            <Setter Property="Foreground" Value="#00FFFF" />
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding dataModel_Search.FLJ_KZH_Result}" Value="NG">
                                            <Setter Property="Foreground" Value="Red" />
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding dataModel_Search.FLJ_KZH_Result}" Value="NA">
                                            <Setter Property="Foreground" Value="Gray" />
                                        </DataTrigger>
                                        <!-- 其他触发器 -->
                                    </Style.Triggers>
                                </Style>
                            </TextBlock.Style>
                            <Run Text="{Binding dataModel_Search.FLJ_KZH_Result}" />
                        </TextBlock>
                    </WrapPanel>
                </Grid>
            </Border>
            <Label Content="分零件：锁扣" Style="{DynamicResource DataDetailTitle}" Height="60"></Label>
            <Border Style="{DynamicResource DataDetailContentBorder}" Width="1032" Height="60">
                <Grid Height="60" Background="Transparent">
                    <Grid.RowDefinitions>
                        <RowDefinition></RowDefinition>
                        <RowDefinition></RowDefinition>
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"></ColumnDefinition>
                        <ColumnDefinition Width="250"></ColumnDefinition>
                    </Grid.ColumnDefinitions>
                    <WrapPanel Grid.Row="0" Grid.ColumnSpan="2">
                        <Label Content="型号码：" Style="{DynamicResource DataDetailFieldTitle_5Words}" ></Label>
                        <Label Content="{Binding dataModel_Search.FLJ_SK_Model}" Style="{DynamicResource DataDetailFieldValue}"></Label>
                        <Label Content="物料编码：" Style="{DynamicResource DataDetailFieldTitle_5Words}"></Label>
                        <Label Content="{Binding dataModel_Search.FLJ_SK_Material}" Style="{DynamicResource DataDetailFieldValue}" ></Label>
                    </WrapPanel>
                    <WrapPanel Grid.Row="1" Grid.ColumnSpan="2">
                        <Label Content="相机存储路径：" Style="{DynamicResource DataDetailFieldTitle_5Words}"></Label>
                        <Button Content="{Binding dataModel_Search.FLJ_SK_Photo}" Tag="path" Style="{DynamicResource FileLinkButton}" Width="666" Height="24" Click="Button_Click"></Button>
                    </WrapPanel>
                    <WrapPanel Grid.Column="1" Grid.RowSpan="2" VerticalAlignment="Center">
                        <Label Content="检测结果：" Style="{DynamicResource DataDetailFieldTitle_6Words}" FontWeight="Bold" ></Label>
                        <!--<Label Content="{Binding dataModel_Search.FLJ_SK_Result}" Grid.Row="1" Style="{DynamicResource DataDetailTestResult}"></Label>-->
                        <TextBlock FontWeight="Bold" FontSize="22" HorizontalAlignment="Center" VerticalAlignment="Center" Width="65" TextAlignment="Center">
                            <TextBlock.Style>
                                <Style TargetType="{x:Type TextBlock}">
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding dataModel_Search.FLJ_SK_Result}" Value="OK">
                                            <Setter Property="Foreground" Value="#00FFFF" />
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding dataModel_Search.FLJ_SK_Result}" Value="NG">
                                            <Setter Property="Foreground" Value="Red" />
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding dataModel_Search.FLJ_SK_Result}" Value="NA">
                                            <Setter Property="Foreground" Value="Gray" />
                                        </DataTrigger>
                                        <!-- 其他触发器 -->
                                    </Style.Triggers>
                                </Style>
                            </TextBlock.Style>
                            <Run Text="{Binding dataModel_Search.FLJ_SK_Result}" />
                        </TextBlock>
                    </WrapPanel>
                </Grid>
            </Border>
            <Label Content="分零件：内密" Style="{DynamicResource DataDetailTitle}" Height="60"></Label>
            <Border Style="{DynamicResource DataDetailContentBorder}" Width="1032" Height="60">
                <Grid Height="60" Background="Transparent">
                    <Grid.RowDefinitions>
                        <RowDefinition></RowDefinition>
                        <RowDefinition></RowDefinition>
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"></ColumnDefinition>
                        <ColumnDefinition Width="190"></ColumnDefinition>
                    </Grid.ColumnDefinitions>
                    <WrapPanel Grid.Row="0" Grid.ColumnSpan="2">
                        <Label Content="相机1存储路径：" Style="{DynamicResource DataDetailFieldTitle_5Words}" ></Label>
                        <Button Content="{Binding dataModel_Search.JCY_NM_Photo1}" Tag="path" Style="{DynamicResource FileLinkButton}" Width="666" Height="24" Click="Button_Click"></Button>
                    </WrapPanel>
                    <WrapPanel Grid.Row="1" Grid.ColumnSpan="2" VerticalAlignment="Center">
                        <Label Content="相机2存储路径：" Style="{DynamicResource DataDetailFieldTitle_5Words}"></Label>
                        <Button Content="{Binding dataModel_Search.JCY_NM_Photo2}" Tag="path" Style="{DynamicResource FileLinkButton}" Width="666" Height="24" Click="Button_Click"></Button>
                    </WrapPanel>
                    <WrapPanel Grid.Column="1" Grid.RowSpan="2" VerticalAlignment="Center">
                        <Label Content="检测结果：" Style="{DynamicResource DataDetailFieldTitle_4Words}" FontWeight="Bold" ></Label>
                        <!--<Label Content="{Binding dataModel_Search.JCY_NM_Result}" Grid.Row="1" Style="{DynamicResource DataDetailTestResult}"></Label>-->
                        <TextBlock FontWeight="Bold" FontSize="22" HorizontalAlignment="Center" VerticalAlignment="Center" Width="65" TextAlignment="Center">
                            <TextBlock.Style>
                                <Style TargetType="{x:Type TextBlock}">
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding dataModel_Search.JCY_NM_Result}" Value="OK">
                                            <Setter Property="Foreground" Value="#00FFFF" />
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding dataModel_Search.JCY_NM_Result}" Value="NG">
                                            <Setter Property="Foreground" Value="Red" />
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding dataModel_Search.JCY_NM_Result}" Value="NA">
                                            <Setter Property="Foreground" Value="Gray" />
                                        </DataTrigger>
                                        <!-- 其他触发器 -->
                                    </Style.Triggers>
                                </Style>
                            </TextBlock.Style>
                            <Run Text="{Binding dataModel_Search.JCY_NM_Result}" />
                        </TextBlock>
                    </WrapPanel>
                </Grid>
            </Border>
            <Label Content="分零件：外密" Style="{DynamicResource DataDetailTitle}" Height="90"></Label>
            <Border Style="{DynamicResource DataDetailContentBorder}" Width="1032" Height="90">
                <Grid Height="90" Background="Transparent">
                    <Grid.RowDefinitions>
                        <RowDefinition></RowDefinition>
                        <RowDefinition></RowDefinition>
                        <RowDefinition></RowDefinition>
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"></ColumnDefinition>
                        <ColumnDefinition Width="190"></ColumnDefinition>
                    </Grid.ColumnDefinitions>
                    <WrapPanel Grid.Row="0" Grid.ColumnSpan="2">
                        <Label Content="型号码：" Style="{DynamicResource DataDetailFieldTitle_5Words}" ></Label>
                        <Label Content="{Binding dataModel_Search.FLJ_WM_Model}" Style="{DynamicResource DataDetailFieldValue}"></Label>
                        <Label Content="物料编码：" Style="{DynamicResource DataDetailFieldTitle_5Words}"></Label>
                        <Label Content="{Binding dataModel_Search.FLJ_WM_Material}" Style="{DynamicResource DataDetailFieldValue}" ></Label>
                    </WrapPanel>
                    <WrapPanel Grid.Row="1" VerticalAlignment="Center">
                        <Label Content="相机1存储路径：" Style="{DynamicResource DataDetailFieldTitle_5Words}"></Label>
                        <Button Content="{Binding dataModel_Search.FLJ_WM_Photo1}" Tag="path" Style="{DynamicResource FileLinkButton}" Width="666" Height="24" Click="Button_Click"></Button>
                    </WrapPanel>
                    <WrapPanel Grid.Row="2" VerticalAlignment="Center">
                        <Label Content="相机2存储路径：" Style="{DynamicResource DataDetailFieldTitle_5Words}"></Label>
                        <Button Content="{Binding dataModel_Search.FLJ_WM_Photo2}" Tag="path" Style="{DynamicResource FileLinkButton}" Width="666" Height="24" Click="Button_Click"></Button>
                    </WrapPanel>
                    <WrapPanel Grid.Column="1" Grid.Row="1" VerticalAlignment="Center">
                        <Label Content="检测结果：" Style="{DynamicResource DataDetailFieldTitle_4Words}" FontWeight="Bold" ></Label>
                        <!--<Label Content="{Binding dataModel_Search.FLJ_WM_Result}" Grid.Row="1" Style="{DynamicResource DataDetailTestResult}"></Label>-->
                        <TextBlock FontWeight="Bold" FontSize="22" HorizontalAlignment="Center" VerticalAlignment="Center" Width="65" TextAlignment="Center">
                            <TextBlock.Style>
                                <Style TargetType="{x:Type TextBlock}">
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding dataModel_Search.FLJ_WM_Result}" Value="OK">
                                            <Setter Property="Foreground" Value="#00FFFF" />
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding dataModel_Search.FLJ_WM_Result}" Value="NG">
                                            <Setter Property="Foreground" Value="Red" />
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding dataModel_Search.FLJ_WM_Result}" Value="NA">
                                            <Setter Property="Foreground" Value="Gray" />
                                        </DataTrigger>
                                        <!-- 其他触发器 -->
                                    </Style.Triggers>
                                </Style>
                            </TextBlock.Style>
                            <Run Text="{Binding dataModel_Search.FLJ_WM_Result}" />
                        </TextBlock>
                    </WrapPanel>
                </Grid>
            </Border>
            <Label Content="分零件：内密气密" Style="{DynamicResource DataDetailTitle}" Height="30"></Label>
            <Border Style="{DynamicResource DataDetailContentBorder}" Width="1032" Height="30">
                <WrapPanel Grid.Row="1" Grid.ColumnSpan="2">
                    <Label Content="气密检测程序号：" Style="{DynamicResource DataDetailFieldTitle_5Words}"></Label>
                    <Label Content="{Binding dataModel_Search.JCY_QM_Program}" Style="{DynamicResource DataDetailFieldValue_Tiny}"></Label>
                    <Label Content="气密检测压力值：" Style="{DynamicResource DataDetailFieldTitle_5Words}" Margin="65,0,0,0"></Label>
                    <Label Content="{Binding dataModel_Search.JCY_QM_Pressure}" Style="{DynamicResource DataDetailFieldValue_Tiny}"></Label>
                    <Label Content="气密检测泄漏值：" Style="{DynamicResource DataDetailFieldTitle_5Words}" Margin="65,0,0,0"></Label>
                    <Label Content="{Binding dataModel_Search.JCY_QM_Leak}" Style="{DynamicResource DataDetailFieldValue_Tiny}"></Label>
                    <Label Content="气密检测结果：" Style="{DynamicResource DataDetailFieldTitle_6Words}" Margin="65,0,0,0" FontWeight="Bold"  ></Label>
                    <!--<Label Content="{Binding dataModel_Search.JCY_QM_Result}" Grid.Row="1" Style="{DynamicResource DataDetailTestResult}"></Label>-->
                    <TextBlock FontWeight="Bold" FontSize="22" HorizontalAlignment="Center" VerticalAlignment="Center" Width="65" TextAlignment="Center">
                        <TextBlock.Style>
                            <Style TargetType="{x:Type TextBlock}">
                                <Style.Triggers>
                                    <DataTrigger Binding="{Binding dataModel_Search.JCY_QM_Result}" Value="OK">
                                        <Setter Property="Foreground" Value="#00FFFF" />
                                    </DataTrigger>
                                    <DataTrigger Binding="{Binding dataModel_Search.JCY_QM_Result}" Value="NG">
                                        <Setter Property="Foreground" Value="Red" />
                                    </DataTrigger>
                                    <DataTrigger Binding="{Binding dataModel_Search.JCY_QM_Result}" Value="NA">
                                        <Setter Property="Foreground" Value="Gray" />
                                    </DataTrigger>
                                    <!-- 其他触发器 -->
                                </Style.Triggers>
                            </Style>
                        </TextBlock.Style>
                            <Run Text="{Binding dataModel_Search.JCY_QM_Result}" />
                    </TextBlock>
                </WrapPanel>
            </Border>
        </WrapPanel>
    </Grid>
</UserControl>
