﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Models;
using System.Diagnostics.CodeAnalysis;
using Utils;

namespace Services
{
    public class WorkPieceDataCaptureRespository
    {
        private readonly IServiceProvider _serviceProvider;
        public WorkPieceDataCaptureRespository(IServiceProvider serviceProvider)
        {
            this._serviceProvider = serviceProvider;
        }
        /// <summary>
        /// 获取数据
        /// </summary>
        /// <returns></returns>
        public FuncResult<List<tb_WorkPieceDataCapture_Realtime>> loadData()
        {
            var result = new FuncResult<List<tb_WorkPieceDataCapture_Realtime>>();
            try
            {
                var _context = this._serviceProvider.GetService<AppDbContext>();
                if (_context != null)
                {
                    result.obj_Return = _context.tb_workpiecedatacapture_realtime.Where(eee => eee.dDate == null).ToList();
                    result.bSuccess = true;
                }
                else
                {
                    result.bSuccess = false;
                    result.strMsg = "无法获取到数据库服务";
                }
            }
            catch (Exception ex)
            {
                result.setException(ex);
            }
            return result;
        }
        /// <summary>
        /// 新增数据
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public FuncResult<int> saveData(tb_WorkPieceDataCapture_Realtime model)
        {
            var result = new FuncResult<int>();
            try
            {
                var _context = this._serviceProvider.GetService<AppDbContext>();
                if (_context != null)
                {
                    _context.tb_workpiecedatacapture_realtime.Add(model);
                    result.bSuccess = true;
                    result.obj_Return = _context.SaveChanges();
                }
                else
                {
                    result.bSuccess = false;
                    result.strMsg = "无法获取到数据库服务";
                }
            }
            catch (Exception ex)
            {
                result.setException(ex);
                result.obj_Return = 0;
            }
            return result;
        }


        /// <summary>
        /// 获取今日产量与24小时产量简单统计数据
        /// </summary>
        /// <returns></returns>
        public FuncResult<ProduceSummary> getProduceSummary()
        {
            var result = new FuncResult<ProduceSummary>();
            var summary = new ProduceSummary();
            int nowyear, nowmonth, nowday;
            try
            {
                var _context = this._serviceProvider.GetService<AppDbContext>();
                if (_context != null)
                {
                    nowyear = DateTime.Now.Year;
                    nowmonth = DateTime.Now.Month;
                    nowday = DateTime.Now.Day;
                    var data = new
                    {
                        CL_OK_24Hour = _context.tb_workpiecedatacapture_realtime
                            .Where(w => w.dDate >= DateTime.Now.AddDays(-1) && w.dDate <= DateTime.Now && w.ZC_Result == "OK")
                            .Count(),
                        CL_NG_24Hour = _context.tb_workpiecedatacapture_realtime
                            .Where(w => w.dDate >= DateTime.Now.AddDays(-1) && w.dDate <= DateTime.Now && w.ZC_Result == "NG")
                            .Count(),
                        CL_OK_Today = _context.tb_workpiecedatacapture_realtime
                            .Where(w => w.dDate >= new DateTime(nowyear, nowmonth, nowday) && w.dDate <= new DateTime(nowyear, nowmonth, nowday).AddDays(1).AddTicks(-1) && w.ZC_Result == "OK")
                            .Count(),
                        CL_NG_Today = _context.tb_workpiecedatacapture_realtime
                            .Where(w => w.dDate >= new DateTime(nowyear, nowmonth, nowday) && w.dDate <= new DateTime(nowyear, nowmonth, nowday).AddDays(1).AddTicks(-1) && w.ZC_Result == "NG")
                            .Count()
                    };
                    summary.OK24Hour = data.CL_OK_24Hour;   //24小时的OK产量
                    summary.NG24Hour = data.CL_NG_24Hour;   //24小时的NG产量
                    summary.production24Hour = data.CL_OK_24Hour + data.CL_NG_24Hour;   //24小时的总产量
                    summary.OKRate24Hour = MathMethods.CalculatePercentage(summary.OK24Hour, summary.production24Hour);    //24小时良品率
                    summary.OKToday = data.CL_OK_Today; //今日OK产量
                    summary.NGToday = data.CL_NG_Today; //今日NG产量
                    summary.productionToday = data.CL_OK_Today + data.CL_NG_Today;  //今日总产量
                    summary.OKRateToday = MathMethods.CalculatePercentage(summary.OKToday, summary.productionToday);   //今日良品率
                    result.bSuccess = true;
                    result.obj_Return = summary;
                }
                else
                {
                    result.bSuccess = false;
                    result.strMsg = "无法获取到数据库服务";
                }
            }
            catch (Exception ex)
            {
                result.setException(ex);
            }
            return result;
        }

        /// <summary>
        /// 到实时数据表里根据查询条件查询数据
        /// </summary>
        /// <param name="conditions"></param>
        /// <param name="dbSet"></param>
        /// <returns></returns>
        public FuncResult<List<tb_WorkPieceDataCapture_Realtime>> SearchDataFromRealTimeTable(model_SearchCondition conditions, DbSet<tb_WorkPieceDataCapture_Realtime> dbSet)
        {
            var result = new FuncResult<List<tb_WorkPieceDataCapture_Realtime>>();
            try
            {
                var query = from wpc in dbSet
                            where (conditions.ZC_SerialNo == null ? true : wpc.ZC_SerialNo.Contains(conditions.ZC_SerialNo)) &&
                                  (conditions.ZC_Model == null ? true : wpc.ZC_Model.Contains(conditions.ZC_Model)) &&
                                  (conditions.dDate_Begin == null ? true : wpc.dDate >= new DateTime(conditions.dDate_Begin.Value.Year, conditions.dDate_Begin.Value.Month, conditions.dDate_Begin.Value.Day, conditions.dDate_Begin.Value.Hour, conditions.dDate_Begin.Value.Minute, 0)) &&
                                  (conditions.dDate_End == null ? true : wpc.dDate <= new DateTime(conditions.dDate_End.Value.Year, conditions.dDate_End.Value.Month, conditions.dDate_End.Value.Day, conditions.dDate_End.Value.Hour, conditions.dDate_End.Value.Minute, 59)) &&
                                  (conditions.ZC_LaserNo == null ? true : wpc.ZC_LaserNo.Contains(conditions.ZC_LaserNo)) &&
                                  (conditions.ZC_Result == null ? true : wpc.ZC_Result.Contains(conditions.ZC_Result.Replace("全部", ""))) &&
                                  (conditions.FLJ_ZT_Model == null ? true : wpc.FLJ_ZT_Model.Contains(conditions.FLJ_ZT_Model)) &&
                                  (conditions.FLJ_ZT_Material == null ? true : wpc.FLJ_ZT_Material.Contains(conditions.FLJ_ZT_Material)) &&
                                  (conditions.FLJ_ZT_Result == null ? true : wpc.FLJ_ZT_Result.Contains(conditions.FLJ_ZT_Result.Replace("全部", ""))) &&
                                  (conditions.FLJ_ZT_Begin == null ? true : wpc.FLJ_ZT_Value >= conditions.FLJ_ZT_Begin) &&
                                  (conditions.FLJ_ZT_End == null ? true : wpc.FLJ_ZT_Value <= conditions.FLJ_ZT_End) &&
                                  (conditions.FLJ_YCQ_Model == null ? true : wpc.FLJ_YCQ_Model.Contains(conditions.FLJ_YCQ_Model)) &&
                                  (conditions.FLJ_YCQ_Material == null ? true : wpc.FLJ_YCQ_Material.Contains(conditions.FLJ_YCQ_Material)) &&
                                  (conditions.FLJ_GLH_Model == null ? true : wpc.FLJ_GLH_Model.Contains(conditions.FLJ_GLH_Model)) &&
                                  (conditions.FLJ_GLH_Material == null ? true : wpc.FLJ_GLH_Material.Contains(conditions.FLJ_GLH_Material)) &&
                                  (conditions.FLJ_GLH_Result == null ? true : wpc.FLJ_GLH_Result.Contains(conditions.FLJ_GLH_Result.Replace("全部", ""))) &&
                                  (conditions.FLJ_GLH_Begin == null ? true : wpc.FLJ_GLH_Value >= conditions.FLJ_GLH_Begin) &&
                                  (conditions.FLJ_GLH_End == null ? true : wpc.FLJ_GLH_Value <= conditions.FLJ_GLH_End) &&
                                  (conditions.FLJ_ECQ_Model == null ? true : wpc.FLJ_ECQ_Model.Contains(conditions.FLJ_ECQ_Model)) &&
                                  (conditions.FLJ_ECQ_Material == null ? true : wpc.FLJ_ECQ_Material.Contains(conditions.FLJ_ECQ_Material)) &&
                                  (conditions.FLJ_ECQ_Result == null ? true : wpc.FLJ_ECQ_Result.Contains(conditions.FLJ_ECQ_Result.Replace("全部", ""))) &&
                                  (conditions.FLJ_ECQ_Begin == null ? true : wpc.FLJ_ECQ_Value >= conditions.FLJ_ECQ_Begin) &&
                                  (conditions.FLJ_ECQ_End == null ? true : wpc.FLJ_ECQ_Value <= conditions.FLJ_ECQ_End) &&
                                  (conditions.FLJ_KZH_Model == null ? true : wpc.FLJ_KZH_Model.Contains(conditions.FLJ_KZH_Model)) &&
                                  (conditions.FLJ_KZH_Material == null ? true : wpc.FLJ_KZH_Material.Contains(conditions.FLJ_KZH_Material)) &&
                                  (conditions.FLJ_KZH_Result == null ? true : wpc.FLJ_KZH_Result.Contains(conditions.FLJ_KZH_Result.Replace("全部", ""))) &&
                                  (conditions.FLJ_KZH_Begin == null ? true : wpc.FLJ_KZH_Value >= conditions.FLJ_KZH_Begin) &&
                                  (conditions.FLJ_KZH_End == null ? true : wpc.FLJ_KZH_Value <= conditions.FLJ_KZH_End) &&
                                  (conditions.FLJ_SK_Model == null ? true : wpc.FLJ_SK_Model.Contains(conditions.FLJ_SK_Model)) &&
                                  (conditions.FLJ_SK_Material == null ? true : wpc.FLJ_SK_Material.Contains(conditions.FLJ_SK_Material)) &&
                                  (conditions.FLJ_SK_Result == null ? true : wpc.FLJ_SK_Result.Contains(conditions.FLJ_SK_Result.Replace("全部", ""))) &&
                                  (conditions.JCY_QM_Result == null ? true : wpc.JCY_QM_Result.Contains(conditions.JCY_QM_Result.Replace("全部", ""))) &&
                                  (conditions.JCY_QM_Program == null ? true : wpc.JCY_QM_Program == conditions.JCY_QM_Program) &&
                                  (conditions.JCY_QM_Pressure_Begin == null ? true : wpc.JCY_QM_Pressure >= conditions.JCY_QM_Pressure_Begin) &&
                                  (conditions.JCY_QM_Pressure_End == null ? true : wpc.JCY_QM_Pressure <= conditions.JCY_QM_Pressure_End) &&
                                  (conditions.JCY_QM_Leak_Begin == null ? true : wpc.JCY_QM_Leak >= conditions.JCY_QM_Leak_Begin) &&
                                  (conditions.JCY_QM_Leak_End == null ? true : wpc.JCY_QM_Leak <= conditions.JCY_QM_Leak_End) &&
                                  (conditions.JCY_NM_Result == null ? true : wpc.JCY_NM_Result.Contains(conditions.JCY_NM_Result.Replace("全部", ""))) &&
                                   (conditions.FLJ_WM_Result == null ? true : wpc.FLJ_WM_Result.Contains(conditions.FLJ_WM_Result.Replace("全部", "")))
                            orderby wpc.ZC_Time descending
                            select wpc;
                var list = query.ToList();
                result.obj_Return = list;
                result.bSuccess = true;
            }
            catch (Exception ex)
            {
                result.setException(ex);
            }
            return result;
        }
        /// <summary>
        /// 到实时数据表里根据查询条件查询数据
        /// </summary>
        /// <param name="conditions"></param>
        /// <param name="dbSet"></param>
        /// <returns></returns>
        public FuncResult<List<tb_WorkPieceDataCapture_History_2024>> SearchDataFrom2024YearHistoryTable(model_SearchCondition conditions, DbSet<tb_WorkPieceDataCapture_History_2024> dbSet)
        {
            var result = new FuncResult<List<tb_WorkPieceDataCapture_History_2024>>();
            try
            {
                var query = from wpc in dbSet
                            where (conditions.ZC_SerialNo == null ? true : wpc.ZC_SerialNo.Contains(conditions.ZC_SerialNo)) &&
                                   (conditions.ZC_Model == null ? true : wpc.ZC_Model.Contains(conditions.ZC_Model)) &&
                                  (conditions.dDate_Begin == null ? true : wpc.dDate >= new DateTime(conditions.dDate_Begin.Value.Year, conditions.dDate_Begin.Value.Month, conditions.dDate_Begin.Value.Day, conditions.dDate_Begin.Value.Hour, conditions.dDate_Begin.Value.Minute, 0)) &&
                                  (conditions.dDate_End == null ? true : wpc.dDate <= new DateTime(conditions.dDate_End.Value.Year, conditions.dDate_End.Value.Month, conditions.dDate_End.Value.Day, conditions.dDate_End.Value.Hour, conditions.dDate_End.Value.Minute, 59)) &&
  (conditions.ZC_LaserNo == null ? true : wpc.ZC_LaserNo.Contains(conditions.ZC_LaserNo)) &&
  (conditions.ZC_Result == null ? true : wpc.ZC_Result.Contains(conditions.ZC_Result.Replace("全部", ""))) &&
  (conditions.FLJ_ZT_Model == null ? true : wpc.FLJ_ZT_Model.Contains(conditions.FLJ_ZT_Model)) &&
  (conditions.FLJ_ZT_Material == null ? true : wpc.FLJ_ZT_Material.Contains(conditions.FLJ_ZT_Material)) &&
  (conditions.FLJ_ZT_Result == null ? true : wpc.FLJ_ZT_Result.Contains(conditions.FLJ_ZT_Result.Replace("全部", ""))) &&
  (conditions.FLJ_ZT_Begin == null ? true : wpc.FLJ_ZT_Value >= conditions.FLJ_ZT_Begin) &&
  (conditions.FLJ_ZT_End == null ? true : wpc.FLJ_ZT_Value <= conditions.FLJ_ZT_End) &&
  (conditions.FLJ_YCQ_Model == null ? true : wpc.FLJ_YCQ_Model.Contains(conditions.FLJ_YCQ_Model)) &&
  (conditions.FLJ_YCQ_Material == null ? true : wpc.FLJ_YCQ_Material.Contains(conditions.FLJ_YCQ_Material)) &&
  (conditions.FLJ_GLH_Model == null ? true : wpc.FLJ_GLH_Model.Contains(conditions.FLJ_GLH_Model)) &&
  (conditions.FLJ_GLH_Material == null ? true : wpc.FLJ_GLH_Material.Contains(conditions.FLJ_GLH_Material)) &&
  (conditions.FLJ_GLH_Result == null ? true : wpc.FLJ_GLH_Result.Contains(conditions.FLJ_GLH_Result.Replace("全部", ""))) &&
  (conditions.FLJ_GLH_Begin == null ? true : wpc.FLJ_GLH_Value >= conditions.FLJ_GLH_Begin) &&
  (conditions.FLJ_GLH_End == null ? true : wpc.FLJ_GLH_Value <= conditions.FLJ_GLH_End) &&
  (conditions.FLJ_ECQ_Model == null ? true : wpc.FLJ_ECQ_Model.Contains(conditions.FLJ_ECQ_Model)) &&
  (conditions.FLJ_ECQ_Material == null ? true : wpc.FLJ_ECQ_Material.Contains(conditions.FLJ_ECQ_Material)) &&
  (conditions.FLJ_ECQ_Result == null ? true : wpc.FLJ_ECQ_Result.Contains(conditions.FLJ_ECQ_Result.Replace("全部", ""))) &&
  (conditions.FLJ_ECQ_Begin == null ? true : wpc.FLJ_ECQ_Value >= conditions.FLJ_ECQ_Begin) &&
  (conditions.FLJ_ECQ_End == null ? true : wpc.FLJ_ECQ_Value <= conditions.FLJ_ECQ_End) &&
  (conditions.FLJ_KZH_Model == null ? true : wpc.FLJ_KZH_Model.Contains(conditions.FLJ_KZH_Model)) &&
  (conditions.FLJ_KZH_Material == null ? true : wpc.FLJ_KZH_Material.Contains(conditions.FLJ_KZH_Material)) &&
  (conditions.FLJ_KZH_Result == null ? true : wpc.FLJ_KZH_Result.Contains(conditions.FLJ_KZH_Result.Replace("全部", ""))) &&
  (conditions.FLJ_KZH_Begin == null ? true : wpc.FLJ_KZH_Value >= conditions.FLJ_KZH_Begin) &&
  (conditions.FLJ_KZH_End == null ? true : wpc.FLJ_KZH_Value <= conditions.FLJ_KZH_End) &&
  (conditions.FLJ_SK_Model == null ? true : wpc.FLJ_SK_Model.Contains(conditions.FLJ_SK_Model)) &&
  (conditions.FLJ_SK_Material == null ? true : wpc.FLJ_SK_Material.Contains(conditions.FLJ_SK_Material)) &&
  (conditions.FLJ_SK_Result == null ? true : wpc.FLJ_SK_Result.Contains(conditions.FLJ_SK_Result.Replace("全部", ""))) &&
  (conditions.JCY_QM_Result == null ? true : wpc.JCY_QM_Result.Contains(conditions.JCY_QM_Result.Replace("全部", ""))) &&
  (conditions.JCY_QM_Program == null ? true : wpc.JCY_QM_Program == conditions.JCY_QM_Program) &&
  (conditions.JCY_QM_Pressure_Begin == null ? true : wpc.JCY_QM_Pressure >= conditions.JCY_QM_Pressure_Begin) &&
  (conditions.JCY_QM_Pressure_End == null ? true : wpc.JCY_QM_Pressure <= conditions.JCY_QM_Pressure_End) &&
  (conditions.JCY_QM_Leak_Begin == null ? true : wpc.JCY_QM_Leak >= conditions.JCY_QM_Leak_Begin) &&
  (conditions.JCY_QM_Leak_End == null ? true : wpc.JCY_QM_Leak <= conditions.JCY_QM_Leak_End) &&
  (conditions.JCY_NM_Result == null ? true : wpc.JCY_NM_Result.Contains(conditions.JCY_NM_Result.Replace("全部", ""))) &&
   (conditions.FLJ_WM_Result == null ? true : wpc.FLJ_WM_Result.Contains(conditions.FLJ_WM_Result.Replace("全部", "")))
                            orderby wpc.ZC_Time descending
                            select wpc;
                var list = query.ToList();
                result.obj_Return = list;
                result.bSuccess = true;
            }
            catch (Exception ex)
            {
                result.setException(ex);
            }
            return result;
        }
        /// <summary>
        /// 到实时数据表里根据查询条件查询数据
        /// </summary>
        /// <param name="conditions"></param>
        /// <param name="dbSet"></param>
        /// <returns></returns>
        public FuncResult<List<tb_WorkPieceDataCapture_History_2025>> SearchDataFrom2025YearHistoryTable(model_SearchCondition conditions, DbSet<tb_WorkPieceDataCapture_History_2025> dbSet)
        {
            var result = new FuncResult<List<tb_WorkPieceDataCapture_History_2025>>();
            try
            {
                var query = from wpc in dbSet
                            where (conditions.ZC_SerialNo == null ? true : wpc.ZC_SerialNo.Contains(conditions.ZC_SerialNo)) &&
                                   (conditions.ZC_Model == null ? true : wpc.ZC_Model.Contains(conditions.ZC_Model)) &&
                                  (conditions.dDate_Begin == null ? true : wpc.dDate >= new DateTime(conditions.dDate_Begin.Value.Year, conditions.dDate_Begin.Value.Month, conditions.dDate_Begin.Value.Day, conditions.dDate_Begin.Value.Hour, conditions.dDate_Begin.Value.Minute, 0)) &&
                                  (conditions.dDate_End == null ? true : wpc.dDate <= new DateTime(conditions.dDate_End.Value.Year, conditions.dDate_End.Value.Month, conditions.dDate_End.Value.Day, conditions.dDate_End.Value.Hour, conditions.dDate_End.Value.Minute, 59)) &&
  (conditions.ZC_LaserNo == null ? true : wpc.ZC_LaserNo.Contains(conditions.ZC_LaserNo)) &&
  (conditions.ZC_Result == null ? true : wpc.ZC_Result.Contains(conditions.ZC_Result.Replace("全部", ""))) &&
  (conditions.FLJ_ZT_Model == null ? true : wpc.FLJ_ZT_Model.Contains(conditions.FLJ_ZT_Model)) &&
  (conditions.FLJ_ZT_Material == null ? true : wpc.FLJ_ZT_Material.Contains(conditions.FLJ_ZT_Material)) &&
  (conditions.FLJ_ZT_Result == null ? true : wpc.FLJ_ZT_Result.Contains(conditions.FLJ_ZT_Result.Replace("全部", ""))) &&
  (conditions.FLJ_ZT_Begin == null ? true : wpc.FLJ_ZT_Value >= conditions.FLJ_ZT_Begin) &&
  (conditions.FLJ_ZT_End == null ? true : wpc.FLJ_ZT_Value <= conditions.FLJ_ZT_End) &&
  (conditions.FLJ_YCQ_Model == null ? true : wpc.FLJ_YCQ_Model.Contains(conditions.FLJ_YCQ_Model)) &&
  (conditions.FLJ_YCQ_Material == null ? true : wpc.FLJ_YCQ_Material.Contains(conditions.FLJ_YCQ_Material)) &&
  (conditions.FLJ_GLH_Model == null ? true : wpc.FLJ_GLH_Model.Contains(conditions.FLJ_GLH_Model)) &&
  (conditions.FLJ_GLH_Material == null ? true : wpc.FLJ_GLH_Material.Contains(conditions.FLJ_GLH_Material)) &&
  (conditions.FLJ_GLH_Result == null ? true : wpc.FLJ_GLH_Result.Contains(conditions.FLJ_GLH_Result.Replace("全部", ""))) &&
  (conditions.FLJ_GLH_Begin == null ? true : wpc.FLJ_GLH_Value >= conditions.FLJ_GLH_Begin) &&
  (conditions.FLJ_GLH_End == null ? true : wpc.FLJ_GLH_Value <= conditions.FLJ_GLH_End) &&
  (conditions.FLJ_ECQ_Model == null ? true : wpc.FLJ_ECQ_Model.Contains(conditions.FLJ_ECQ_Model)) &&
  (conditions.FLJ_ECQ_Material == null ? true : wpc.FLJ_ECQ_Material.Contains(conditions.FLJ_ECQ_Material)) &&
  (conditions.FLJ_ECQ_Result == null ? true : wpc.FLJ_ECQ_Result.Contains(conditions.FLJ_ECQ_Result.Replace("全部", ""))) &&
  (conditions.FLJ_ECQ_Begin == null ? true : wpc.FLJ_ECQ_Value >= conditions.FLJ_ECQ_Begin) &&
  (conditions.FLJ_ECQ_End == null ? true : wpc.FLJ_ECQ_Value <= conditions.FLJ_ECQ_End) &&
  (conditions.FLJ_KZH_Model == null ? true : wpc.FLJ_KZH_Model.Contains(conditions.FLJ_KZH_Model)) &&
  (conditions.FLJ_KZH_Material == null ? true : wpc.FLJ_KZH_Material.Contains(conditions.FLJ_KZH_Material)) &&
  (conditions.FLJ_KZH_Result == null ? true : wpc.FLJ_KZH_Result.Contains(conditions.FLJ_KZH_Result.Replace("全部", ""))) &&
  (conditions.FLJ_KZH_Begin == null ? true : wpc.FLJ_KZH_Value >= conditions.FLJ_KZH_Begin) &&
  (conditions.FLJ_KZH_End == null ? true : wpc.FLJ_KZH_Value <= conditions.FLJ_KZH_End) &&
  (conditions.FLJ_SK_Model == null ? true : wpc.FLJ_SK_Model.Contains(conditions.FLJ_SK_Model)) &&
  (conditions.FLJ_SK_Material == null ? true : wpc.FLJ_SK_Material.Contains(conditions.FLJ_SK_Material)) &&
  (conditions.FLJ_SK_Result == null ? true : wpc.FLJ_SK_Result.Contains(conditions.FLJ_SK_Result.Replace("全部", ""))) &&
  (conditions.JCY_QM_Result == null ? true : wpc.JCY_QM_Result.Contains(conditions.JCY_QM_Result.Replace("全部", ""))) &&
  (conditions.JCY_QM_Program == null ? true : wpc.JCY_QM_Program == conditions.JCY_QM_Program) &&
  (conditions.JCY_QM_Pressure_Begin == null ? true : wpc.JCY_QM_Pressure >= conditions.JCY_QM_Pressure_Begin) &&
  (conditions.JCY_QM_Pressure_End == null ? true : wpc.JCY_QM_Pressure <= conditions.JCY_QM_Pressure_End) &&
  (conditions.JCY_QM_Leak_Begin == null ? true : wpc.JCY_QM_Leak >= conditions.JCY_QM_Leak_Begin) &&
  (conditions.JCY_QM_Leak_End == null ? true : wpc.JCY_QM_Leak <= conditions.JCY_QM_Leak_End) &&
  (conditions.JCY_NM_Result == null ? true : wpc.JCY_NM_Result.Contains(conditions.JCY_NM_Result.Replace("全部", ""))) &&
   (conditions.FLJ_WM_Result == null ? true : wpc.FLJ_WM_Result.Contains(conditions.FLJ_WM_Result.Replace("全部", "")))
                            orderby wpc.ZC_Time descending
                            select wpc;
                var list = query.ToList();
                result.obj_Return = list;
                result.bSuccess = true;
            }
            catch (Exception ex)
            {
                result.setException(ex);
            }
            return result;
        }
        /// <summary>
        /// 到实时数据表里根据查询条件查询数据
        /// </summary>
        /// <param name="conditions"></param>
        /// <param name="dbSet"></param>
        /// <returns></returns>
        public FuncResult<List<tb_WorkPieceDataCapture_History_2026>> SearchDataFrom2026YearHistoryTable(model_SearchCondition conditions, DbSet<tb_WorkPieceDataCapture_History_2026> dbSet)
        {
            var result = new FuncResult<List<tb_WorkPieceDataCapture_History_2026>>();
            try
            {
                var query = from wpc in dbSet
                            where (conditions.ZC_SerialNo == null ? true : wpc.ZC_SerialNo.Contains(conditions.ZC_SerialNo)) &&
                                   (conditions.ZC_Model == null ? true : wpc.ZC_Model.Contains(conditions.ZC_Model)) &&
                                  (conditions.dDate_Begin == null ? true : wpc.dDate >= new DateTime(conditions.dDate_Begin.Value.Year, conditions.dDate_Begin.Value.Month, conditions.dDate_Begin.Value.Day, conditions.dDate_Begin.Value.Hour, conditions.dDate_Begin.Value.Minute, 0)) &&
                                  (conditions.dDate_End == null ? true : wpc.dDate <= new DateTime(conditions.dDate_End.Value.Year, conditions.dDate_End.Value.Month, conditions.dDate_End.Value.Day, conditions.dDate_End.Value.Hour, conditions.dDate_End.Value.Minute, 59)) &&
  (conditions.ZC_LaserNo == null ? true : wpc.ZC_LaserNo.Contains(conditions.ZC_LaserNo)) &&
  (conditions.ZC_Result == null ? true : wpc.ZC_Result.Contains(conditions.ZC_Result.Replace("全部", ""))) &&
  (conditions.FLJ_ZT_Model == null ? true : wpc.FLJ_ZT_Model.Contains(conditions.FLJ_ZT_Model)) &&
  (conditions.FLJ_ZT_Material == null ? true : wpc.FLJ_ZT_Material.Contains(conditions.FLJ_ZT_Material)) &&
  (conditions.FLJ_ZT_Result == null ? true : wpc.FLJ_ZT_Result.Contains(conditions.FLJ_ZT_Result.Replace("全部", ""))) &&
  (conditions.FLJ_ZT_Begin == null ? true : wpc.FLJ_ZT_Value >= conditions.FLJ_ZT_Begin) &&
  (conditions.FLJ_ZT_End == null ? true : wpc.FLJ_ZT_Value <= conditions.FLJ_ZT_End) &&
  (conditions.FLJ_YCQ_Model == null ? true : wpc.FLJ_YCQ_Model.Contains(conditions.FLJ_YCQ_Model)) &&
  (conditions.FLJ_YCQ_Material == null ? true : wpc.FLJ_YCQ_Material.Contains(conditions.FLJ_YCQ_Material)) &&
  (conditions.FLJ_GLH_Model == null ? true : wpc.FLJ_GLH_Model.Contains(conditions.FLJ_GLH_Model)) &&
  (conditions.FLJ_GLH_Material == null ? true : wpc.FLJ_GLH_Material.Contains(conditions.FLJ_GLH_Material)) &&
  (conditions.FLJ_GLH_Result == null ? true : wpc.FLJ_GLH_Result.Contains(conditions.FLJ_GLH_Result.Replace("全部", ""))) &&
  (conditions.FLJ_GLH_Begin == null ? true : wpc.FLJ_GLH_Value >= conditions.FLJ_GLH_Begin) &&
  (conditions.FLJ_GLH_End == null ? true : wpc.FLJ_GLH_Value <= conditions.FLJ_GLH_End) &&
  (conditions.FLJ_ECQ_Model == null ? true : wpc.FLJ_ECQ_Model.Contains(conditions.FLJ_ECQ_Model)) &&
  (conditions.FLJ_ECQ_Material == null ? true : wpc.FLJ_ECQ_Material.Contains(conditions.FLJ_ECQ_Material)) &&
  (conditions.FLJ_ECQ_Result == null ? true : wpc.FLJ_ECQ_Result.Contains(conditions.FLJ_ECQ_Result.Replace("全部", ""))) &&
  (conditions.FLJ_ECQ_Begin == null ? true : wpc.FLJ_ECQ_Value >= conditions.FLJ_ECQ_Begin) &&
  (conditions.FLJ_ECQ_End == null ? true : wpc.FLJ_ECQ_Value <= conditions.FLJ_ECQ_End) &&
  (conditions.FLJ_KZH_Model == null ? true : wpc.FLJ_KZH_Model.Contains(conditions.FLJ_KZH_Model)) &&
  (conditions.FLJ_KZH_Material == null ? true : wpc.FLJ_KZH_Material.Contains(conditions.FLJ_KZH_Material)) &&
  (conditions.FLJ_KZH_Result == null ? true : wpc.FLJ_KZH_Result.Contains(conditions.FLJ_KZH_Result.Replace("全部", ""))) &&
  (conditions.FLJ_KZH_Begin == null ? true : wpc.FLJ_KZH_Value >= conditions.FLJ_KZH_Begin) &&
  (conditions.FLJ_KZH_End == null ? true : wpc.FLJ_KZH_Value <= conditions.FLJ_KZH_End) &&
  (conditions.FLJ_SK_Model == null ? true : wpc.FLJ_SK_Model.Contains(conditions.FLJ_SK_Model)) &&
  (conditions.FLJ_SK_Material == null ? true : wpc.FLJ_SK_Material.Contains(conditions.FLJ_SK_Material)) &&
  (conditions.FLJ_SK_Result == null ? true : wpc.FLJ_SK_Result.Contains(conditions.FLJ_SK_Result.Replace("全部", ""))) &&
  (conditions.JCY_QM_Result == null ? true : wpc.JCY_QM_Result.Contains(conditions.JCY_QM_Result.Replace("全部", ""))) &&
  (conditions.JCY_QM_Program == null ? true : wpc.JCY_QM_Program == conditions.JCY_QM_Program) &&
  (conditions.JCY_QM_Pressure_Begin == null ? true : wpc.JCY_QM_Pressure >= conditions.JCY_QM_Pressure_Begin) &&
  (conditions.JCY_QM_Pressure_End == null ? true : wpc.JCY_QM_Pressure <= conditions.JCY_QM_Pressure_End) &&
  (conditions.JCY_QM_Leak_Begin == null ? true : wpc.JCY_QM_Leak >= conditions.JCY_QM_Leak_Begin) &&
  (conditions.JCY_QM_Leak_End == null ? true : wpc.JCY_QM_Leak <= conditions.JCY_QM_Leak_End) &&
  (conditions.JCY_NM_Result == null ? true : wpc.JCY_NM_Result.Contains(conditions.JCY_NM_Result.Replace("全部", ""))) &&
   (conditions.FLJ_WM_Result == null ? true : wpc.FLJ_WM_Result.Contains(conditions.FLJ_WM_Result.Replace("全部", "")))
                            orderby wpc.ZC_Time descending
                            select wpc;
                var list = query.ToList();
                result.obj_Return = list;
                result.bSuccess = true;
            }
            catch (Exception ex)
            {
                result.setException(ex);
            }
            return result;
        }
        /// <summary>
        /// 到实时数据表里根据查询条件查询数据
        /// </summary>
        /// <param name="conditions"></param>
        /// <param name="dbSet"></param>
        /// <returns></returns>
        public FuncResult<List<tb_WorkPieceDataCapture_History_2027>> SearchDataFrom2027YearHistoryTable(model_SearchCondition conditions, DbSet<tb_WorkPieceDataCapture_History_2027> dbSet)
        {
            var result = new FuncResult<List<tb_WorkPieceDataCapture_History_2027>>();
            try
            {
                var query = from wpc in dbSet
                            where (conditions.ZC_SerialNo == null ? true : wpc.ZC_SerialNo.Contains(conditions.ZC_SerialNo)) &&
                                  (conditions.ZC_Model == null ? true : wpc.ZC_Model.Contains(conditions.ZC_Model)) &&
                                  (conditions.dDate_Begin == null ? true : wpc.dDate >= new DateTime(conditions.dDate_Begin.Value.Year, conditions.dDate_Begin.Value.Month, conditions.dDate_Begin.Value.Day, conditions.dDate_Begin.Value.Hour, conditions.dDate_Begin.Value.Minute, 0)) &&
                                  (conditions.dDate_End == null ? true : wpc.dDate <= new DateTime(conditions.dDate_End.Value.Year, conditions.dDate_End.Value.Month, conditions.dDate_End.Value.Day, conditions.dDate_End.Value.Hour, conditions.dDate_End.Value.Minute, 59)) &&
  (conditions.ZC_LaserNo == null ? true : wpc.ZC_LaserNo.Contains(conditions.ZC_LaserNo)) &&
  (conditions.ZC_Result == null ? true : wpc.ZC_Result.Contains(conditions.ZC_Result.Replace("全部", ""))) &&
  (conditions.FLJ_ZT_Model == null ? true : wpc.FLJ_ZT_Model.Contains(conditions.FLJ_ZT_Model)) &&
  (conditions.FLJ_ZT_Material == null ? true : wpc.FLJ_ZT_Material.Contains(conditions.FLJ_ZT_Material)) &&
  (conditions.FLJ_ZT_Result == null ? true : wpc.FLJ_ZT_Result.Contains(conditions.FLJ_ZT_Result.Replace("全部", ""))) &&
  (conditions.FLJ_ZT_Begin == null ? true : wpc.FLJ_ZT_Value >= conditions.FLJ_ZT_Begin) &&
  (conditions.FLJ_ZT_End == null ? true : wpc.FLJ_ZT_Value <= conditions.FLJ_ZT_End) &&
  (conditions.FLJ_YCQ_Model == null ? true : wpc.FLJ_YCQ_Model.Contains(conditions.FLJ_YCQ_Model)) &&
  (conditions.FLJ_YCQ_Material == null ? true : wpc.FLJ_YCQ_Material.Contains(conditions.FLJ_YCQ_Material)) &&
  (conditions.FLJ_GLH_Model == null ? true : wpc.FLJ_GLH_Model.Contains(conditions.FLJ_GLH_Model)) &&
  (conditions.FLJ_GLH_Material == null ? true : wpc.FLJ_GLH_Material.Contains(conditions.FLJ_GLH_Material)) &&
  (conditions.FLJ_GLH_Result == null ? true : wpc.FLJ_GLH_Result.Contains(conditions.FLJ_GLH_Result.Replace("全部", ""))) &&
  (conditions.FLJ_GLH_Begin == null ? true : wpc.FLJ_GLH_Value >= conditions.FLJ_GLH_Begin) &&
  (conditions.FLJ_GLH_End == null ? true : wpc.FLJ_GLH_Value <= conditions.FLJ_GLH_End) &&
  (conditions.FLJ_ECQ_Model == null ? true : wpc.FLJ_ECQ_Model.Contains(conditions.FLJ_ECQ_Model)) &&
  (conditions.FLJ_ECQ_Material == null ? true : wpc.FLJ_ECQ_Material.Contains(conditions.FLJ_ECQ_Material)) &&
  (conditions.FLJ_ECQ_Result == null ? true : wpc.FLJ_ECQ_Result.Contains(conditions.FLJ_ECQ_Result.Replace("全部", ""))) &&
  (conditions.FLJ_ECQ_Begin == null ? true : wpc.FLJ_ECQ_Value >= conditions.FLJ_ECQ_Begin) &&
  (conditions.FLJ_ECQ_End == null ? true : wpc.FLJ_ECQ_Value <= conditions.FLJ_ECQ_End) &&
  (conditions.FLJ_KZH_Model == null ? true : wpc.FLJ_KZH_Model.Contains(conditions.FLJ_KZH_Model)) &&
  (conditions.FLJ_KZH_Material == null ? true : wpc.FLJ_KZH_Material.Contains(conditions.FLJ_KZH_Material)) &&
  (conditions.FLJ_KZH_Result == null ? true : wpc.FLJ_KZH_Result.Contains(conditions.FLJ_KZH_Result.Replace("全部", ""))) &&
  (conditions.FLJ_KZH_Begin == null ? true : wpc.FLJ_KZH_Value >= conditions.FLJ_KZH_Begin) &&
  (conditions.FLJ_KZH_End == null ? true : wpc.FLJ_KZH_Value <= conditions.FLJ_KZH_End) &&
  (conditions.FLJ_SK_Model == null ? true : wpc.FLJ_SK_Model.Contains(conditions.FLJ_SK_Model)) &&
  (conditions.FLJ_SK_Material == null ? true : wpc.FLJ_SK_Material.Contains(conditions.FLJ_SK_Material)) &&
  (conditions.FLJ_SK_Result == null ? true : wpc.FLJ_SK_Result.Contains(conditions.FLJ_SK_Result.Replace("全部", ""))) &&
  (conditions.JCY_QM_Result == null ? true : wpc.JCY_QM_Result.Contains(conditions.JCY_QM_Result.Replace("全部", ""))) &&
  (conditions.JCY_QM_Program == null ? true : wpc.JCY_QM_Program == conditions.JCY_QM_Program) &&
  (conditions.JCY_QM_Pressure_Begin == null ? true : wpc.JCY_QM_Pressure >= conditions.JCY_QM_Pressure_Begin) &&
  (conditions.JCY_QM_Pressure_End == null ? true : wpc.JCY_QM_Pressure <= conditions.JCY_QM_Pressure_End) &&
  (conditions.JCY_QM_Leak_Begin == null ? true : wpc.JCY_QM_Leak >= conditions.JCY_QM_Leak_Begin) &&
  (conditions.JCY_QM_Leak_End == null ? true : wpc.JCY_QM_Leak <= conditions.JCY_QM_Leak_End) &&
  (conditions.JCY_NM_Result == null ? true : wpc.JCY_NM_Result.Contains(conditions.JCY_NM_Result.Replace("全部", ""))) &&
   (conditions.FLJ_WM_Result == null ? true : wpc.FLJ_WM_Result.Contains(conditions.FLJ_WM_Result.Replace("全部", "")))
                            orderby wpc.ZC_Time descending
                            select wpc;
                var list = query.ToList();
                result.obj_Return = list;
                result.bSuccess = true;
            }
            catch (Exception ex)
            {
                result.setException(ex);
            }
            return result;
        }
        /// <summary>
        /// 到实时数据表里根据查询条件查询数据
        /// </summary>
        /// <param name="conditions"></param>
        /// <param name="dbSet"></param>
        /// <returns></returns>
        public FuncResult<List<tb_WorkPieceDataCapture_History_2028>> SearchDataFrom2028YearHistoryTable(model_SearchCondition conditions, DbSet<tb_WorkPieceDataCapture_History_2028> dbSet)
        {
            var result = new FuncResult<List<tb_WorkPieceDataCapture_History_2028>>();
            try
            {
                var query = from wpc in dbSet
                            where (conditions.ZC_SerialNo == null ? true : wpc.ZC_SerialNo.Contains(conditions.ZC_SerialNo)) &&
                                  (conditions.ZC_Model == null ? true : wpc.ZC_Model.Contains(conditions.ZC_Model)) &&
                                  (conditions.dDate_Begin == null ? true : wpc.dDate >= new DateTime(conditions.dDate_Begin.Value.Year, conditions.dDate_Begin.Value.Month, conditions.dDate_Begin.Value.Day, conditions.dDate_Begin.Value.Hour, conditions.dDate_Begin.Value.Minute, 0)) &&
                                  (conditions.dDate_End == null ? true : wpc.dDate <= new DateTime(conditions.dDate_End.Value.Year, conditions.dDate_End.Value.Month, conditions.dDate_End.Value.Day, conditions.dDate_End.Value.Hour, conditions.dDate_End.Value.Minute, 59)) &&
  (conditions.ZC_LaserNo == null ? true : wpc.ZC_LaserNo.Contains(conditions.ZC_LaserNo)) &&
  (conditions.ZC_Result == null ? true : wpc.ZC_Result.Contains(conditions.ZC_Result.Replace("全部", ""))) &&
  (conditions.FLJ_ZT_Model == null ? true : wpc.FLJ_ZT_Model.Contains(conditions.FLJ_ZT_Model)) &&
  (conditions.FLJ_ZT_Material == null ? true : wpc.FLJ_ZT_Material.Contains(conditions.FLJ_ZT_Material)) &&
  (conditions.FLJ_ZT_Result == null ? true : wpc.FLJ_ZT_Result.Contains(conditions.FLJ_ZT_Result.Replace("全部", ""))) &&
  (conditions.FLJ_ZT_Begin == null ? true : wpc.FLJ_ZT_Value >= conditions.FLJ_ZT_Begin) &&
  (conditions.FLJ_ZT_End == null ? true : wpc.FLJ_ZT_Value <= conditions.FLJ_ZT_End) &&
  (conditions.FLJ_YCQ_Model == null ? true : wpc.FLJ_YCQ_Model.Contains(conditions.FLJ_YCQ_Model)) &&
  (conditions.FLJ_YCQ_Material == null ? true : wpc.FLJ_YCQ_Material.Contains(conditions.FLJ_YCQ_Material)) &&
  (conditions.FLJ_GLH_Model == null ? true : wpc.FLJ_GLH_Model.Contains(conditions.FLJ_GLH_Model)) &&
  (conditions.FLJ_GLH_Material == null ? true : wpc.FLJ_GLH_Material.Contains(conditions.FLJ_GLH_Material)) &&
  (conditions.FLJ_GLH_Result == null ? true : wpc.FLJ_GLH_Result.Contains(conditions.FLJ_GLH_Result.Replace("全部", ""))) &&
  (conditions.FLJ_GLH_Begin == null ? true : wpc.FLJ_GLH_Value >= conditions.FLJ_GLH_Begin) &&
  (conditions.FLJ_GLH_End == null ? true : wpc.FLJ_GLH_Value <= conditions.FLJ_GLH_End) &&
  (conditions.FLJ_ECQ_Model == null ? true : wpc.FLJ_ECQ_Model.Contains(conditions.FLJ_ECQ_Model)) &&
  (conditions.FLJ_ECQ_Material == null ? true : wpc.FLJ_ECQ_Material.Contains(conditions.FLJ_ECQ_Material)) &&
  (conditions.FLJ_ECQ_Result == null ? true : wpc.FLJ_ECQ_Result.Contains(conditions.FLJ_ECQ_Result.Replace("全部", ""))) &&
  (conditions.FLJ_ECQ_Begin == null ? true : wpc.FLJ_ECQ_Value >= conditions.FLJ_ECQ_Begin) &&
  (conditions.FLJ_ECQ_End == null ? true : wpc.FLJ_ECQ_Value <= conditions.FLJ_ECQ_End) &&
  (conditions.FLJ_KZH_Model == null ? true : wpc.FLJ_KZH_Model.Contains(conditions.FLJ_KZH_Model)) &&
  (conditions.FLJ_KZH_Material == null ? true : wpc.FLJ_KZH_Material.Contains(conditions.FLJ_KZH_Material)) &&
  (conditions.FLJ_KZH_Result == null ? true : wpc.FLJ_KZH_Result.Contains(conditions.FLJ_KZH_Result.Replace("全部", ""))) &&
  (conditions.FLJ_KZH_Begin == null ? true : wpc.FLJ_KZH_Value >= conditions.FLJ_KZH_Begin) &&
  (conditions.FLJ_KZH_End == null ? true : wpc.FLJ_KZH_Value <= conditions.FLJ_KZH_End) &&
  (conditions.FLJ_SK_Model == null ? true : wpc.FLJ_SK_Model.Contains(conditions.FLJ_SK_Model)) &&
  (conditions.FLJ_SK_Material == null ? true : wpc.FLJ_SK_Material.Contains(conditions.FLJ_SK_Material)) &&
  (conditions.FLJ_SK_Result == null ? true : wpc.FLJ_SK_Result.Contains(conditions.FLJ_SK_Result.Replace("全部", ""))) &&
  (conditions.JCY_QM_Result == null ? true : wpc.JCY_QM_Result.Contains(conditions.JCY_QM_Result.Replace("全部", ""))) &&
  (conditions.JCY_QM_Program == null ? true : wpc.JCY_QM_Program == conditions.JCY_QM_Program) &&
  (conditions.JCY_QM_Pressure_Begin == null ? true : wpc.JCY_QM_Pressure >= conditions.JCY_QM_Pressure_Begin) &&
  (conditions.JCY_QM_Pressure_End == null ? true : wpc.JCY_QM_Pressure <= conditions.JCY_QM_Pressure_End) &&
  (conditions.JCY_QM_Leak_Begin == null ? true : wpc.JCY_QM_Leak >= conditions.JCY_QM_Leak_Begin) &&
  (conditions.JCY_QM_Leak_End == null ? true : wpc.JCY_QM_Leak <= conditions.JCY_QM_Leak_End) &&
  (conditions.JCY_NM_Result == null ? true : wpc.JCY_NM_Result.Contains(conditions.JCY_NM_Result.Replace("全部", ""))) &&
   (conditions.FLJ_WM_Result == null ? true : wpc.FLJ_WM_Result.Contains(conditions.FLJ_WM_Result.Replace("全部", "")))
                            orderby wpc.ZC_Time descending
                            select wpc;
                var list = query.ToList();
                result.obj_Return = list;
                result.bSuccess = true;
            }
            catch (Exception ex)
            {
                result.setException(ex);
            }
            return result;
        }
        /// <summary>
        /// 到实时数据表里根据查询条件查询数据
        /// </summary>
        /// <param name="conditions"></param>
        /// <param name="dbSet"></param>
        /// <returns></returns>
        public FuncResult<List<tb_WorkPieceDataCapture_History_2029>> SearchDataFrom2029YearHistoryTable(model_SearchCondition conditions, DbSet<tb_WorkPieceDataCapture_History_2029> dbSet)
        {
            var result = new FuncResult<List<tb_WorkPieceDataCapture_History_2029>>();
            try
            {
                var query = from wpc in dbSet
                            where (conditions.ZC_SerialNo == null ? true : wpc.ZC_SerialNo.Contains(conditions.ZC_SerialNo)) &&
                                   (conditions.ZC_Model == null ? true : wpc.ZC_Model.Contains(conditions.ZC_Model)) &&
                                  (conditions.dDate_Begin == null ? true : wpc.dDate >= new DateTime(conditions.dDate_Begin.Value.Year, conditions.dDate_Begin.Value.Month, conditions.dDate_Begin.Value.Day, conditions.dDate_Begin.Value.Hour, conditions.dDate_Begin.Value.Minute, 0)) &&
                                  (conditions.dDate_End == null ? true : wpc.dDate <= new DateTime(conditions.dDate_End.Value.Year, conditions.dDate_End.Value.Month, conditions.dDate_End.Value.Day, conditions.dDate_End.Value.Hour, conditions.dDate_End.Value.Minute, 59)) &&
  (conditions.ZC_LaserNo == null ? true : wpc.ZC_LaserNo.Contains(conditions.ZC_LaserNo)) &&
  (conditions.ZC_Result == null ? true : wpc.ZC_Result.Contains(conditions.ZC_Result.Replace("全部", ""))) &&
  (conditions.FLJ_ZT_Model == null ? true : wpc.FLJ_ZT_Model.Contains(conditions.FLJ_ZT_Model)) &&
  (conditions.FLJ_ZT_Material == null ? true : wpc.FLJ_ZT_Material.Contains(conditions.FLJ_ZT_Material)) &&
  (conditions.FLJ_ZT_Result == null ? true : wpc.FLJ_ZT_Result.Contains(conditions.FLJ_ZT_Result.Replace("全部", ""))) &&
  (conditions.FLJ_ZT_Begin == null ? true : wpc.FLJ_ZT_Value >= conditions.FLJ_ZT_Begin) &&
  (conditions.FLJ_ZT_End == null ? true : wpc.FLJ_ZT_Value <= conditions.FLJ_ZT_End) &&
  (conditions.FLJ_YCQ_Model == null ? true : wpc.FLJ_YCQ_Model.Contains(conditions.FLJ_YCQ_Model)) &&
  (conditions.FLJ_YCQ_Material == null ? true : wpc.FLJ_YCQ_Material.Contains(conditions.FLJ_YCQ_Material)) &&
  (conditions.FLJ_GLH_Model == null ? true : wpc.FLJ_GLH_Model.Contains(conditions.FLJ_GLH_Model)) &&
  (conditions.FLJ_GLH_Material == null ? true : wpc.FLJ_GLH_Material.Contains(conditions.FLJ_GLH_Material)) &&
  (conditions.FLJ_GLH_Result == null ? true : wpc.FLJ_GLH_Result.Contains(conditions.FLJ_GLH_Result.Replace("全部", ""))) &&
  (conditions.FLJ_GLH_Begin == null ? true : wpc.FLJ_GLH_Value >= conditions.FLJ_GLH_Begin) &&
  (conditions.FLJ_GLH_End == null ? true : wpc.FLJ_GLH_Value <= conditions.FLJ_GLH_End) &&
  (conditions.FLJ_ECQ_Model == null ? true : wpc.FLJ_ECQ_Model.Contains(conditions.FLJ_ECQ_Model)) &&
  (conditions.FLJ_ECQ_Material == null ? true : wpc.FLJ_ECQ_Material.Contains(conditions.FLJ_ECQ_Material)) &&
  (conditions.FLJ_ECQ_Result == null ? true : wpc.FLJ_ECQ_Result.Contains(conditions.FLJ_ECQ_Result.Replace("全部", ""))) &&
  (conditions.FLJ_ECQ_Begin == null ? true : wpc.FLJ_ECQ_Value >= conditions.FLJ_ECQ_Begin) &&
  (conditions.FLJ_ECQ_End == null ? true : wpc.FLJ_ECQ_Value <= conditions.FLJ_ECQ_End) &&
  (conditions.FLJ_KZH_Model == null ? true : wpc.FLJ_KZH_Model.Contains(conditions.FLJ_KZH_Model)) &&
  (conditions.FLJ_KZH_Material == null ? true : wpc.FLJ_KZH_Material.Contains(conditions.FLJ_KZH_Material)) &&
  (conditions.FLJ_KZH_Result == null ? true : wpc.FLJ_KZH_Result.Contains(conditions.FLJ_KZH_Result.Replace("全部", ""))) &&
  (conditions.FLJ_KZH_Begin == null ? true : wpc.FLJ_KZH_Value >= conditions.FLJ_KZH_Begin) &&
  (conditions.FLJ_KZH_End == null ? true : wpc.FLJ_KZH_Value <= conditions.FLJ_KZH_End) &&
  (conditions.FLJ_SK_Model == null ? true : wpc.FLJ_SK_Model.Contains(conditions.FLJ_SK_Model)) &&
  (conditions.FLJ_SK_Material == null ? true : wpc.FLJ_SK_Material.Contains(conditions.FLJ_SK_Material)) &&
  (conditions.FLJ_SK_Result == null ? true : wpc.FLJ_SK_Result.Contains(conditions.FLJ_SK_Result.Replace("全部", ""))) &&
  (conditions.JCY_QM_Result == null ? true : wpc.JCY_QM_Result.Contains(conditions.JCY_QM_Result.Replace("全部", ""))) &&
  (conditions.JCY_QM_Program == null ? true : wpc.JCY_QM_Program == conditions.JCY_QM_Program) &&
  (conditions.JCY_QM_Pressure_Begin == null ? true : wpc.JCY_QM_Pressure >= conditions.JCY_QM_Pressure_Begin) &&
  (conditions.JCY_QM_Pressure_End == null ? true : wpc.JCY_QM_Pressure <= conditions.JCY_QM_Pressure_End) &&
  (conditions.JCY_QM_Leak_Begin == null ? true : wpc.JCY_QM_Leak >= conditions.JCY_QM_Leak_Begin) &&
  (conditions.JCY_QM_Leak_End == null ? true : wpc.JCY_QM_Leak <= conditions.JCY_QM_Leak_End) &&
  (conditions.JCY_NM_Result == null ? true : wpc.JCY_NM_Result.Contains(conditions.JCY_NM_Result.Replace("全部", ""))) &&
   (conditions.FLJ_WM_Result == null ? true : wpc.FLJ_WM_Result.Contains(conditions.FLJ_WM_Result.Replace("全部", "")))
                            orderby wpc.ZC_Time descending
                            select wpc;
                var list = query.ToList();
                result.obj_Return = list;
                result.bSuccess = true;
            }
            catch (Exception ex)
            {
                result.setException(ex);
            }
            return result;
        }
        /// <summary>
        /// 到实时数据表里根据查询条件查询数据
        /// </summary>
        /// <param name="conditions"></param>
        /// <param name="dbSet"></param>
        /// <returns></returns>
        public FuncResult<List<tb_WorkPieceDataCapture_History_2030>> SearchDataFrom2030YearHistoryTable(model_SearchCondition conditions, DbSet<tb_WorkPieceDataCapture_History_2030> dbSet)
        {
            var result = new FuncResult<List<tb_WorkPieceDataCapture_History_2030>>();
            try
            {
                var query = from wpc in dbSet
                            where (conditions.ZC_SerialNo == null ? true : wpc.ZC_SerialNo.Contains(conditions.ZC_SerialNo)) &&
                                    (conditions.ZC_Model == null ? true : wpc.ZC_Model.Contains(conditions.ZC_Model)) &&
                                  (conditions.dDate_Begin == null ? true : wpc.dDate >= new DateTime(conditions.dDate_Begin.Value.Year, conditions.dDate_Begin.Value.Month, conditions.dDate_Begin.Value.Day, conditions.dDate_Begin.Value.Hour, conditions.dDate_Begin.Value.Minute, 0)) &&
                                  (conditions.dDate_End == null ? true : wpc.dDate <= new DateTime(conditions.dDate_End.Value.Year, conditions.dDate_End.Value.Month, conditions.dDate_End.Value.Day, conditions.dDate_End.Value.Hour, conditions.dDate_End.Value.Minute, 59)) &&
  (conditions.ZC_LaserNo == null ? true : wpc.ZC_LaserNo.Contains(conditions.ZC_LaserNo)) &&
  (conditions.ZC_Result == null ? true : wpc.ZC_Result.Contains(conditions.ZC_Result.Replace("全部", ""))) &&
  (conditions.FLJ_ZT_Model == null ? true : wpc.FLJ_ZT_Model.Contains(conditions.FLJ_ZT_Model)) &&
  (conditions.FLJ_ZT_Material == null ? true : wpc.FLJ_ZT_Material.Contains(conditions.FLJ_ZT_Material)) &&
  (conditions.FLJ_ZT_Result == null ? true : wpc.FLJ_ZT_Result.Contains(conditions.FLJ_ZT_Result.Replace("全部", ""))) &&
  (conditions.FLJ_ZT_Begin == null ? true : wpc.FLJ_ZT_Value >= conditions.FLJ_ZT_Begin) &&
  (conditions.FLJ_ZT_End == null ? true : wpc.FLJ_ZT_Value <= conditions.FLJ_ZT_End) &&
  (conditions.FLJ_YCQ_Model == null ? true : wpc.FLJ_YCQ_Model.Contains(conditions.FLJ_YCQ_Model)) &&
  (conditions.FLJ_YCQ_Material == null ? true : wpc.FLJ_YCQ_Material.Contains(conditions.FLJ_YCQ_Material)) &&
  (conditions.FLJ_GLH_Model == null ? true : wpc.FLJ_GLH_Model.Contains(conditions.FLJ_GLH_Model)) &&
  (conditions.FLJ_GLH_Material == null ? true : wpc.FLJ_GLH_Material.Contains(conditions.FLJ_GLH_Material)) &&
  (conditions.FLJ_GLH_Result == null ? true : wpc.FLJ_GLH_Result.Contains(conditions.FLJ_GLH_Result.Replace("全部", ""))) &&
  (conditions.FLJ_GLH_Begin == null ? true : wpc.FLJ_GLH_Value >= conditions.FLJ_GLH_Begin) &&
  (conditions.FLJ_GLH_End == null ? true : wpc.FLJ_GLH_Value <= conditions.FLJ_GLH_End) &&
  (conditions.FLJ_ECQ_Model == null ? true : wpc.FLJ_ECQ_Model.Contains(conditions.FLJ_ECQ_Model)) &&
  (conditions.FLJ_ECQ_Material == null ? true : wpc.FLJ_ECQ_Material.Contains(conditions.FLJ_ECQ_Material)) &&
  (conditions.FLJ_ECQ_Result == null ? true : wpc.FLJ_ECQ_Result.Contains(conditions.FLJ_ECQ_Result.Replace("全部", ""))) &&
  (conditions.FLJ_ECQ_Begin == null ? true : wpc.FLJ_ECQ_Value >= conditions.FLJ_ECQ_Begin) &&
  (conditions.FLJ_ECQ_End == null ? true : wpc.FLJ_ECQ_Value <= conditions.FLJ_ECQ_End) &&
  (conditions.FLJ_KZH_Model == null ? true : wpc.FLJ_KZH_Model.Contains(conditions.FLJ_KZH_Model)) &&
  (conditions.FLJ_KZH_Material == null ? true : wpc.FLJ_KZH_Material.Contains(conditions.FLJ_KZH_Material)) &&
  (conditions.FLJ_KZH_Result == null ? true : wpc.FLJ_KZH_Result.Contains(conditions.FLJ_KZH_Result.Replace("全部", ""))) &&
  (conditions.FLJ_KZH_Begin == null ? true : wpc.FLJ_KZH_Value >= conditions.FLJ_KZH_Begin) &&
  (conditions.FLJ_KZH_End == null ? true : wpc.FLJ_KZH_Value <= conditions.FLJ_KZH_End) &&
  (conditions.FLJ_SK_Model == null ? true : wpc.FLJ_SK_Model.Contains(conditions.FLJ_SK_Model)) &&
  (conditions.FLJ_SK_Material == null ? true : wpc.FLJ_SK_Material.Contains(conditions.FLJ_SK_Material)) &&
  (conditions.FLJ_SK_Result == null ? true : wpc.FLJ_SK_Result.Contains(conditions.FLJ_SK_Result.Replace("全部", ""))) &&
  (conditions.JCY_QM_Result == null ? true : wpc.JCY_QM_Result.Contains(conditions.JCY_QM_Result.Replace("全部", ""))) &&
  (conditions.JCY_QM_Program == null ? true : wpc.JCY_QM_Program == conditions.JCY_QM_Program) &&
  (conditions.JCY_QM_Pressure_Begin == null ? true : wpc.JCY_QM_Pressure >= conditions.JCY_QM_Pressure_Begin) &&
  (conditions.JCY_QM_Pressure_End == null ? true : wpc.JCY_QM_Pressure <= conditions.JCY_QM_Pressure_End) &&
  (conditions.JCY_QM_Leak_Begin == null ? true : wpc.JCY_QM_Leak >= conditions.JCY_QM_Leak_Begin) &&
  (conditions.JCY_QM_Leak_End == null ? true : wpc.JCY_QM_Leak <= conditions.JCY_QM_Leak_End) &&
  (conditions.JCY_NM_Result == null ? true : wpc.JCY_NM_Result.Contains(conditions.JCY_NM_Result.Replace("全部", ""))) &&
   (conditions.FLJ_WM_Result == null ? true : wpc.FLJ_WM_Result.Contains(conditions.FLJ_WM_Result.Replace("全部", "")))
                            orderby wpc.ZC_Time descending
                            select wpc;
                var list = query.ToList();
                result.obj_Return = list;
                result.bSuccess = true;
            }
            catch (Exception ex)
            {
                result.setException(ex);
            }
            return result;
        }
        /// <summary>
        /// 根据总成激光码查询数据
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public FuncResult<tb_WorkPieceDataCapture_Realtime> getByLaserNoFromRealTime(string lazerNo)
        {
            var result = new FuncResult<tb_WorkPieceDataCapture_Realtime>();
            try
            {
                var _context = this._serviceProvider.GetService<AppDbContext>();
                if (_context != null)
                {
                    var obj = _context.tb_workpiecedatacapture_realtime.FirstOrDefault(w => w.ZC_LaserNo == lazerNo);
                    result.obj_Return = obj;
                    result.bSuccess = true;
                }
                else
                {
                    result.bSuccess = false;
                    result.strMsg = "无法获取到数据库服务";
                }
            }
            catch (Exception ex)
            {
                result.setException(ex);
            }
            return result;
        }
        /// <summary>
        /// 根据总成标识码查询数据
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public FuncResult<tb_WorkPieceDataCapture_Realtime> getBySerialNoFromRealTime(string serialNo)
        {
            var result = new FuncResult<tb_WorkPieceDataCapture_Realtime>();
            try
            {
                var _context = this._serviceProvider.GetService<AppDbContext>();
                if (_context != null)
                {
                    var obj = _context.tb_workpiecedatacapture_realtime.FirstOrDefault(w => w.ZC_SerialNo == serialNo);
                    result.obj_Return = obj;
                    result.bSuccess = true;
                }
                else
                {
                    result.bSuccess = false;
                    result.strMsg = "无法获取到数据库服务";
                }
            }
            catch (Exception ex)
            {
                result.setException(ex);
            }
            return result;
        }
    }
}
