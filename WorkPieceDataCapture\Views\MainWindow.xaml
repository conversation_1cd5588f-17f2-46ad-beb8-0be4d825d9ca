﻿<Window xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
xmlns:d="http://schemas.microsoft.com/expression/blend/2008" xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    x:Class="WorkPieceDataCapture.Views.MainWindow" mc:Ignorable="d" Title="工件生产数据采集分析系统" Height="1080" Width="1920"  WindowState="Maximized"  >
    <Viewbox Stretch="Fill">
        <Grid Style="{DynamicResource GeneralGrid}" Height="1080" Width="1920">
            <Grid.RowDefinitions>
                <RowDefinition Height="65"></RowDefinition>
                <RowDefinition Height="58"></RowDefinition>
                <RowDefinition Height="*"></RowDefinition>
            </Grid.RowDefinitions>
            <Grid Style="{DynamicResource GridTitle}">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="50"></ColumnDefinition>
                    <ColumnDefinition Width="100 "></ColumnDefinition>
                    <ColumnDefinition Width="600"></ColumnDefinition>
                    <ColumnDefinition Width="*"></ColumnDefinition>
                    <ColumnDefinition></ColumnDefinition>
                    <ColumnDefinition></ColumnDefinition>
                    <ColumnDefinition ></ColumnDefinition>
                    <ColumnDefinition Width="250"></ColumnDefinition>
                    <ColumnDefinition></ColumnDefinition>
                    <ColumnDefinition></ColumnDefinition>
                </Grid.ColumnDefinitions>
                <Image Grid.Column="1" Source="pack://siteoforigin:,,,/Resources/Logo.png" Margin="0,10,0,10"></Image>
                <Label Grid.Column="2" Content="工件生产数据采集与分析系统" VerticalAlignment="Center" FontFamily="Arial" FontWeight="Bold"  FontSize="33" Foreground="LightGray" ></Label>
                <Label Name="lbl_Week" Grid.Column="7" Grid.ColumnSpan="2" Content="{Binding NowDateTime}" VerticalAlignment="Center" FontFamily="Arial"  FontSize="20" Foreground="LightGray" HorizontalAlignment="left"></Label> <!--2024年3月22日 星期五 23:23:23-->
                <WrapPanel Grid.Column="9" Background="Transparent"  VerticalAlignment="Center" HorizontalAlignment="Center" PreviewMouseDown="WrapPanel_PreviewMouseDown" >
                    <Image Source="pack://siteoforigin:,,,/Resources/Exit.png" Height="33"></Image>
                    <Label Content="退出"  VerticalAlignment="Center" FontFamily="Arial" FontWeight="Bold"  FontSize="22" Foreground="LightGray" ></Label>
                </WrapPanel>
            </Grid>
            <WrapPanel Grid.Row="1" Margin="10,5">
                <Button Style="{DynamicResource ToolBarButton}" Margin="40,6,0,6" Click="Menu_Click" Tag="实时数据采集" >
                    <WrapPanel Style="{DynamicResource ToolBarButtonPanel}">
                        <Image Source="pack://siteoforigin:,,,/Resources/实时流数据.png" Style="{DynamicResource ToolBarButtonImage}"></Image>
                        <TextBlock Text="实时数据采集"  Style="{DynamicResource ToolBarButtonText}"></TextBlock>
                    </WrapPanel>
                </Button>
                <Button Style="{DynamicResource ToolBarButton}" Tag="历史数据查询" Click="Menu_Click"  >
                    <WrapPanel Style="{DynamicResource ToolBarButtonPanel}">
                        <Image Source="pack://siteoforigin:,,,/Resources/实时流数据.png" Style="{DynamicResource ToolBarButtonImage}"></Image>
                        <TextBlock Text="历史数据查询"  Style="{DynamicResource ToolBarButtonText}"></TextBlock>
                    </WrapPanel>
                </Button>
                <Button Style="{DynamicResource ToolBarButton}" Tag="NG占比分析" Click="Menu_Click" >
                    <WrapPanel Style="{DynamicResource ToolBarButtonPanel}">
                        <Image Source="pack://siteoforigin:,,,/Resources/实时流数据.png" Style="{DynamicResource ToolBarButtonImage}"></Image>
                        <TextBlock Text="NG占比分析"  Style="{DynamicResource ToolBarButtonText}"></TextBlock>
                    </WrapPanel>
                </Button>
                <Button Style="{DynamicResource ToolBarButton}" Tag="NG数量分布"  Click="Menu_Click" >
                    <WrapPanel Style="{DynamicResource ToolBarButtonPanel}" Tag="NG数量统计" >
                        <Image Source="pack://siteoforigin:,,,/Resources/实时流数据.png" Style="{DynamicResource ToolBarButtonImage}"></Image>
                        <TextBlock Text="检测数值分布"  Style="{DynamicResource ToolBarButtonText}"></TextBlock>
                    </WrapPanel>
                </Button>
                <Button Style="{DynamicResource ToolBarButton}" Tag="坐标数据图表"  Click="Menu_Click" >
                    <WrapPanel Style="{DynamicResource ToolBarButtonPanel}">
                        <Image Source="pack://siteoforigin:,,,/Resources/实时流数据.png" Style="{DynamicResource ToolBarButtonImage}"></Image>
                        <TextBlock Text="坐标数据图表"  Style="{DynamicResource ToolBarButtonText}"></TextBlock>
                    </WrapPanel>
                </Button>
            </WrapPanel>
            <ContentControl Name="contentControl" Grid.Row="2" Content="{Binding uc}" ></ContentControl>
        </Grid>
    </Viewbox>
</Window>
