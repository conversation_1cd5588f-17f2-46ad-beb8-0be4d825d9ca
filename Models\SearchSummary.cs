﻿namespace Models
{
    public class SearchSummary
    {
        public int RecordCount { get; set; }//查询出记录总数
        #region OK
        public int ZC_OK { get; set; }//总成OK数量
        public int ZT_OK { get; set; }  //分零件主体OK数量
        public int YCQ_OK { get; set; }  //分零件一次圈OK数量
        public int GLH_OK { get; set; } //分零件隔离环OK数量
        public int ECQ_OK { get; set; } //二次圈OK数量
        public int KZH_OK { get; set; } //安全环OK数量
        public int SK_OK { get; set; }  //锁扣OK数量
        public int NM_OK { get; set; }  //内密OK数量
        public int QM_OK { get; set; }  //气密OK数量
        public int WM_OK { get; set; }  //气密OK数量
        #endregion
        #region NG
        public int ZC_NG { get; set; }  //总成NG数量
        public int ZT_NG { get; set; }  //分零件主体NG数量
        public int YCQ_NG { get; set; }  //分零件一次圈NG数量
        public int GLH_NG { get; set; } //分零件隔离环NG数量
        public int ECQ_NG { get; set; } //二次圈NG数量
        public int KZH_NG { get; set; } //安全环NG数量
        public int SK_NG { get; set; }  //锁扣NG数量
        public int NM_NG { get; set; }  //内密NG数量
        public int QM_NG { get; set; }  //气密NG数量
        public int WM_NG { get; set; }  //外密NG数量

        #endregion    
        #region NGPercent
        public string? ZC_Percent { get; set; }  //总成良品率
        public string? ZT_Percent { get; set; }  //分零件主体良品率
        public string? YCQ_Percent{ get; set; }  //分零件一次圈良品率
        public string? GLH_Percent { get; set; } //分零件隔离环良品率
        public string? ECQ_Percent { get; set; } //二次圈良品率
        public string? KZH_Percent { get; set; } //安全环良品率
        public string? SK_Percent { get; set; }  //锁扣NG良品率
        public string? NM_Percent { get; set; }  //内密NG良品率
        public string? QM_Percent { get; set; }  //气密NG良品率
        public string? WM_Percent { get; set; }  //外密NG良品率
        #endregion
    }
}
