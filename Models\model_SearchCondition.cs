﻿using System.ComponentModel;

namespace Models
{
    public class model_SearchCondition : INotifyPropertyChanged
    {
        #region 私有变量
        private string? _ZC_SerialNo; // 总成设备标识码
        private string? _ZC_Model; // 总成型号
        private string? _ZC_Time;   // 总成生产时间
        private DateTime? _dDate_Begin; // 总成生产时间
        private DateTime? _dDate_End; // 总成生产时间
        private string? _ZC_LaserNo; // 总成激光打码编码
        private string? _ZC_Result="全部"; // 总成综合结果
        private string? _FLJ_ZT_Model; // 分零件：主体型号码
        private string? _FLJ_ZT_Material; // 分零件：主体物料编码
        private string? _FLJ_ZT_Result="全部"; // 分零件：主体异物位移检测结果
        private double? _FLJ_ZT_Begin; // 分零件：主体异物位移检测参数起始值
        private double? _FLJ_ZT_End; // 分零件：主体异物位移检测参数结束值
        private string? _FLJ_YCQ_Model; // 分零件：一次圈型号码
        private string? _FLJ_YCQ_Material; // 分零件：一次圈物料编码
        //private string? _FLJ_YCQ_Result="全部"; // 分零件：一次圈检测结果
        //private double? _FLJ_YCQ_Begin; // 分零件：一次圈位移检测参数起始值
        //private double? _FLJ_YCQ_End; // 分零件：一次圈位移检测参数结束值
        private string? _FLJ_GLH_Model; // 分零件：隔离环型号码
        private string? _FLJ_GLH_Material; // 分零件：隔离环物料编码
        private string? _FLJ_GLH_Result="全部"; // 分零件：隔离环检测结果
        private double? _FLJ_GLH_Begin; // 分零件：隔离环位移检测参数起始值
        private double? _FLJ_GLH_End; // 分零件：隔离环位移检测参数结束值
        private string? _FLJ_ECQ_Model; // 分零件：二次圈型号码
        private string? _FLJ_ECQ_Material; // 分零件：二次圈物料编码
        private string? _FLJ_ECQ_Result="全部"; // 分零件：二次圈检测结果
        private double? _FLJ_ECQ_Begin; // 分零件：二次圈位移检测参数起始值
        private double? _FLJ_ECQ_End; // 分零件：二次圈位移检测参数结束值
        private string? _FLJ_KZH_Model; // 分零件：安全环型号码
        private string? _FLJ_KZH_Material; // 分零件：安全环物料编码
        private string? _FLJ_KZH_Result="全部"; // 分零件：安全环检测结果
        private double? _FLJ_KZH_Begin; // 分零件：安全环位移检测参数起始值
        private double? _FLJ_KZH_End; // 分零件：安全环位移检测参数结束值
        private string? _FLJ_SK_Model; // 分零件：锁扣型号码
        private string? _FLJ_SK_Material; // 分零件：锁扣物料编码
        private string? _FLJ_SK_Result="全部"; // 分零件：锁扣检测结果
        private double? _FLJ_SK_Begin; // 分零件：安全环位移检测参数起始值
        private double? _FLJ_SK_End; // 分零件：安全环位移检测参数结束值
        private string? _JCY_QM_Result="全部"; // 检测仪：气密检测结果
        private int? _JCY_QM_Program; // 检测仪：气密检测程序号
        private double? _JCY_QM_Pressure_Begin; // 检测仪：气密检测压力值起始值
        private double? _JCY_QM_Pressure_End; // 检测仪：气密检测压力值结束值
        private double? _JCY_QM_Leak_Begin; // 检测仪：气密检测泄漏值起始值
        private double? _JCY_QM_Leak_End; // 检测仪：气密检测泄漏值结束值
        private string? _JCY_NM_Result="全部"; // 分零件：内密检测结果
        private string? _FLJ_WM_Result= "全部"; // 分零件：外密检测结果
        private string? _FLJ_Type; // 分零件类型

        #endregion
        #region 公共属性
        public string? ZC_SerialNo
        {
            get
            {
                return _ZC_SerialNo;
            }
            set
            {
                _ZC_SerialNo = value;
                OnPropertyChanged(nameof(ZC_SerialNo));
            }
        } // 总成设备标识码
        public string? ZC_Model
        {
            get
            {
                return _ZC_Model;
            }
            set
            {
                _ZC_Model = value;
                OnPropertyChanged(nameof(ZC_Model));
            }
        } // 总成型号
        public DateTime? dDate_Begin
        {
            get
            {
                return _dDate_Begin;
            }
            set
            {
                _dDate_Begin = value;
                OnPropertyChanged(nameof(dDate_Begin));
            }
        } // 总成生产时间
        public DateTime? dDate_End
        {
            get
            {
                return _dDate_End;
            }
            set
            {
                _dDate_End = value;
                OnPropertyChanged(nameof(dDate_End));
            }
        }// 总成生产时间
        public string? ZC_LaserNo
        {
            get
            {
                return _ZC_LaserNo;
            }
            set
            {
                _ZC_LaserNo = value;
                OnPropertyChanged(nameof(ZC_LaserNo));
            }
        } // 总成激光打码编码
        public string? ZC_Result
        {
            get
            {
                return _ZC_Result;
            }
            set
            {
                _ZC_Result = value;
                OnPropertyChanged(nameof(ZC_Result));
            }
        } // 总成综合结果
        public string? FLJ_ZT_Model
        {
            get
            {
                return _FLJ_ZT_Model;
            }
            set
            {
                _FLJ_ZT_Model = value;
                OnPropertyChanged(nameof(FLJ_ZT_Model));
            }
        } // 分零件：主体型号码
        public string? FLJ_ZT_Material
        {
            get
            {
                return _FLJ_ZT_Material;
            }
            set
            {
                _FLJ_ZT_Material = value;
                OnPropertyChanged(nameof(FLJ_ZT_Material));
            }
        } // 分零件：主体物料编码
        public string? FLJ_ZT_Result
        {
            get
            {
                return _FLJ_ZT_Result;
            }
            set
            {
                _FLJ_ZT_Result = value;
                OnPropertyChanged(nameof(FLJ_ZT_Result));
            }
        }// 分零件：主体异物位移检测结果
        public double? FLJ_ZT_Begin
        {
            get
            {
                return _FLJ_ZT_Begin;
            }
            set
            {
                _FLJ_ZT_Begin = value;
                OnPropertyChanged(nameof(FLJ_ZT_Begin));
            }
        } // 分零件：主体异物位移检测参数起始值
        public double? FLJ_ZT_End
        {
            get
            {
                return _FLJ_ZT_End;
            }
            set
            {
                _FLJ_ZT_End = value;
                OnPropertyChanged(nameof(FLJ_ZT_End));
            }
        } // 分零件：主体异物位移检测参数结束值
        public string? FLJ_YCQ_Model
        {
            get
            {
                return _FLJ_YCQ_Model;
            }
            set
            {
                _FLJ_YCQ_Model = value;
                OnPropertyChanged(nameof(FLJ_YCQ_Model));
            }
        } // 分零件：一次圈型号码
        public string? FLJ_YCQ_Material
        {
            get
            {
                return _FLJ_YCQ_Material;
            }
            set
            {
                _FLJ_YCQ_Material = value;
                OnPropertyChanged(nameof(FLJ_YCQ_Material));
            }
        } // 分零件：一次圈物料编码
        //public string? FLJ_YCQ_Result
        //{
        //    get
        //    {
        //        return _FLJ_YCQ_Result;
        //    }
        //    set
        //    {
        //        _FLJ_YCQ_Result = value;
        //        OnPropertyChanged(nameof(FLJ_YCQ_Result));
        //    }
        //}  // 分零件：一次圈检测结果
        //public double? FLJ_YCQ_Begin
        //{
        //    get
        //    {
        //        return _FLJ_YCQ_Begin;
        //    }
        //    set
        //    {
        //        _FLJ_YCQ_Begin = value;
        //        OnPropertyChanged(nameof(FLJ_YCQ_Begin));
        //    }
        //} // 分零件：一次圈位移检测参数起始值
        //public double? FLJ_YCQ_End
        //{
        //    get
        //    {
        //        return _FLJ_YCQ_End;
        //    }
        //    set
        //    {
        //        _FLJ_YCQ_End = value;
        //        OnPropertyChanged(nameof(FLJ_YCQ_End));
        //    }
        //} // 分零件：一次圈位移检测参数结束值
        public string? FLJ_GLH_Model
        {
            get
            {
                return _FLJ_GLH_Model;
            }
            set
            {
                _FLJ_GLH_Model = value;
                OnPropertyChanged(nameof(FLJ_GLH_Model));
            }
        } // 分零件：隔离环型号码
        public string? FLJ_GLH_Material
        {
            get
            {
                return _FLJ_GLH_Material;
            }
            set
            {
                _FLJ_GLH_Material = value;
                OnPropertyChanged(nameof(FLJ_GLH_Material));
            }
        } // 分零件：隔离环物料编码
        public string? FLJ_GLH_Result
        {
            get
            {
                return _FLJ_GLH_Result;
            }
            set
            {
                _FLJ_GLH_Result = value;
                OnPropertyChanged(nameof(FLJ_GLH_Result));
            }
        } // 分零件：隔离环检测结果
        public double? FLJ_GLH_Begin
        {
            get
            {
                return _FLJ_GLH_Begin;
            }
            set
            {
                _FLJ_GLH_Begin = value;
                OnPropertyChanged(nameof(FLJ_GLH_Begin));
            }
        } // 分零件：隔离环位移检测参数起始值
        public double? FLJ_GLH_End
        {
            get
            {
                return _FLJ_GLH_End;
            }
            set
            {
                _FLJ_GLH_End = value;
                OnPropertyChanged(nameof(FLJ_GLH_End));
            }
        } // 分零件：隔离环位移检测参数结束值
        public string? FLJ_ECQ_Model
        {
            get
            {
                return _FLJ_ECQ_Model;
            }
            set
            {
                _FLJ_ECQ_Model = value;
                OnPropertyChanged(nameof(FLJ_ECQ_Model));
            }
        } // 分零件：二次圈型号码
        public string? FLJ_ECQ_Material
        {
            get
            {
                return _FLJ_ECQ_Material;
            }
            set
            {
                _FLJ_ECQ_Material = value;
                OnPropertyChanged(nameof(FLJ_ECQ_Material));
            }
        } // 分零件：二次圈物料编码
        public string? FLJ_ECQ_Result
        {
            get
            {
                return _FLJ_ECQ_Result;
            }
            set
            {
                _FLJ_ECQ_Result = value;
                OnPropertyChanged(nameof(FLJ_ECQ_Result));
            }
        } // 分零件：二次圈检测结果
        public double? FLJ_ECQ_Begin
        {
            get
            {
                return _FLJ_ECQ_Begin;
            }
            set
            {
                _FLJ_ECQ_Begin = value;
                OnPropertyChanged(nameof(FLJ_ECQ_Begin));
            }
        } // 分零件：二次圈位移检测参数起始值
        public double? FLJ_ECQ_End
        {
            get
            {
                return _FLJ_ECQ_End;
            }
            set
            {
                _FLJ_ECQ_End = value;
                OnPropertyChanged(nameof(FLJ_ECQ_End));
            }
        } // 分零件：二次圈位移检测参数结束值
        public string? FLJ_KZH_Model
        {
            get
            {
                return _FLJ_KZH_Model;
            }
            set
            {
                _FLJ_KZH_Model = value;
                OnPropertyChanged(nameof(FLJ_KZH_Model));
            }
        } // 分零件：安全环型号码
        public string? FLJ_KZH_Material
        {
            get
            {
                return _FLJ_KZH_Material;
            }
            set
            {
                _FLJ_KZH_Material = value;
                OnPropertyChanged(nameof(FLJ_KZH_Material));
            }
        } // 分零件：安全环物料编码
        public string? FLJ_KZH_Result
        {
            get
            {
                return _FLJ_KZH_Result;
            }
            set
            {
                _FLJ_KZH_Result = value;
                OnPropertyChanged(nameof(FLJ_KZH_Result));
            }
        }// 分零件：安全环检测结果
        public double? FLJ_KZH_Begin
        {
            get
            {
                return _FLJ_KZH_Begin;
            }
            set
            {
                _FLJ_KZH_Begin = value;
                OnPropertyChanged(nameof(FLJ_KZH_Begin));
            }
        } // 分零件：安全环位移检测参数起始值
        public double? FLJ_KZH_End
        {
            get
            {
                return _FLJ_KZH_End;
            }
            set
            {
                _FLJ_KZH_End = value;
                OnPropertyChanged(nameof(FLJ_KZH_End));
            }
        } // 分零件：安全环位移检测参数结束值
        public string? FLJ_SK_Model
        {
            get
            {
                return _FLJ_SK_Model;
            }
            set
            {
                _FLJ_SK_Model = value;
                OnPropertyChanged(nameof(FLJ_SK_Model));
            }
        } // 分零件：锁扣型号码
        public string? FLJ_SK_Material
        {
            get
            {
                return _FLJ_SK_Material;
            }
            set
            {
                _FLJ_SK_Material = value;
                OnPropertyChanged(nameof(FLJ_SK_Material));
            }
        } // 分零件：锁扣物料编码
        public string? FLJ_SK_Result
        {
            get
            {
                return _FLJ_SK_Result;
            }
            set
            {
                _FLJ_SK_Result = value;
                OnPropertyChanged(nameof(FLJ_SK_Result));
            }
        } // 分零件：锁扣检测结果
        public double? FLJ_SK_Begin
        {
            get
            {
                return _FLJ_SK_Begin;
            }
            set
            {
                _FLJ_SK_Begin = value;
                OnPropertyChanged(nameof(FLJ_SK_Begin));
            }
        } // 分零件：安全环位移检测参数起始值
        public double? FLJ_SK_End
        {
            get
            {
                return _FLJ_SK_End;
            }
            set
            {
                _FLJ_SK_End = value;
                OnPropertyChanged(nameof(FLJ_SK_End));
            }
        } // 分零件：安全环位移检测参数结束值
        public string? JCY_QM_Result
        {
            get
            {
                return _JCY_QM_Result;
            }
            set
            {
                _JCY_QM_Result = value;
                OnPropertyChanged(nameof(JCY_QM_Result));
            }
        } // 检测仪：气密检测结果
        public int? JCY_QM_Program
        {
            get
            {
                return _JCY_QM_Program;
            }
            set
            {
                _JCY_QM_Program = value;
                OnPropertyChanged(nameof(JCY_QM_Program));
            }
        } // 检测仪：气密检测程序号
        public double? JCY_QM_Pressure_Begin
        {
            get
            {
                return _JCY_QM_Pressure_Begin;
            }
            set
            {
                _JCY_QM_Pressure_Begin = value;
                OnPropertyChanged(nameof(JCY_QM_Pressure_Begin));
            }
        } // 检测仪：气密检测压力值起始值
        public double? JCY_QM_Pressure_End
        {
            get
            {
                return _JCY_QM_Pressure_End;
            }
            set
            {
                _JCY_QM_Pressure_End = value;
                OnPropertyChanged(nameof(JCY_QM_Pressure_End));
            }
        } // 检测仪：气密检测压力值结束值
        public double? JCY_QM_Leak_Begin
        {
            get
            {
                return _JCY_QM_Leak_Begin;
            }
            set
            {
                _JCY_QM_Leak_Begin = value;
                OnPropertyChanged(nameof(JCY_QM_Leak_Begin));
            }
        } // 检测仪：气密检测泄漏值起始值
        public double? JCY_QM_Leak_End
        {
            get
            {
                return _JCY_QM_Leak_End;
            }
            set
            {
                _JCY_QM_Leak_End = value;
                OnPropertyChanged(nameof(JCY_QM_Leak_End));
            }
        } // 检测仪：气密检测泄漏值结束值
        public string? JCY_NM_Result
        {
            get
            {
                return _JCY_NM_Result;
            }
            set
            {
                _JCY_NM_Result = value;
                OnPropertyChanged(nameof(JCY_NM_Result));
            }
        } // 分零件：内密检测结果
        public string? FLJ_WM_Result
        {
            get
            {
                return _FLJ_WM_Result;
            }
            set
            {
                _FLJ_WM_Result = value;
                OnPropertyChanged(nameof(FLJ_WM_Result));
            }
        } // 分零件：内密检测结果
        public string? FLJ_Type
        {
            get
            {
                return _FLJ_Type;
            }
            set
            {
                _FLJ_Type = value;
                OnPropertyChanged(nameof(FLJ_Type));
            }
        } // 分零件类型

        public event PropertyChangedEventHandler? PropertyChanged;
        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
        #endregion
        #region 预留
        //public string? FLJ_WM_Model { get; set; } // 分零件：外密型号码
        //public string? FLJ_WM_Material { get; set; } // 分零件：外密物料编码
        //public string? FLJ_WM_Result { get; set; } // 分零件：外密检测结果
        //public string? JCY_KTQM_Result { get; set; } // 检测仪：壳体气密检测结果
        //public int? JCY_KTQM_Program { get; set; } // 检测仪：壳体气密程序号
        //public double? JCY_KTQM_Pressure { get; set; } // 检测仪：壳体气密压力值
        //public double? JCY_KTQM_Leak { get; set; } // 检测仪：壳体气密泄漏值
        //public string? FLJ_LH_Model { get; set; } // 分零件：拉坏型号码
        //public string? FLJ_LH_Material { get; set; } // 分零件：拉坏物料编码
        //public string? FLJ_LH_Result { get; set; } // 分零件：拉坏检测结果
        //public string? FLJ_LH_LaserNo { get; set; } // 分零件：拉坏激光打码编码
        #endregion
    }
}
