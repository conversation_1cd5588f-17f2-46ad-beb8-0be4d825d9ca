﻿<UserControl x:Class="WorkPieceDataCapture.Views.uc_Chart_NG" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
 xmlns:d="http://schemas.microsoft.com/expression/blend/2008" xmlns:lvc="clr-namespace:LiveCharts.Wpf;assembly=LiveCharts.Wpf" mc:Ignorable="d"  d:DesignHeight="1000" d:DesignWidth="1920">
    <Viewbox Stretch="Fill">
        <Grid Style="{DynamicResource GeneralGrid}" Height="650" Width="1280">
            <Grid.RowDefinitions>
                <RowDefinition Height="*"></RowDefinition>
                <RowDefinition Height="30"></RowDefinition>
                <RowDefinition Height="370"></RowDefinition>
                <RowDefinition Height="30"></RowDefinition>
                <RowDefinition Height="*"></RowDefinition>
                <RowDefinition></RowDefinition>
            </Grid.RowDefinitions>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="200"></ColumnDefinition>
                <ColumnDefinition Width="*"></ColumnDefinition>
            </Grid.ColumnDefinitions>
            <Label Content="生产日期" Style="{DynamicResource GroupTitle}" Grid.Row="1" ></Label>
            <WrapPanel HorizontalAlignment="Center" Grid.Row="2">
                <Calendar Name="begin" SelectedDate="{Binding conditions.dDate_Begin}" PreviewMouseUp="Calendar_PreviewMouseUp"></Calendar>
                <Label Style="{DynamicResource DataDetailFieldTitle_4Words}" Content="至" HorizontalAlignment="Center"/>
                <Calendar Name="end" SelectedDate="{Binding conditions.dDate_End}" PreviewMouseUp="Calendar_PreviewMouseUp"></Calendar>
            </WrapPanel>
            <Label Content="总成型号" Style="{DynamicResource GroupTitle}" Grid.Row="3"></Label>
            <WrapPanel Grid.Row="4" HorizontalAlignment="Center">
                <TextBox Name="txtModel" Style="{DynamicResource TextBox_Normal}" Text="{Binding conditions.ZC_Model}" Grid.Row="3" Height="30" Width="190" Margin="5"></TextBox>
                <Button Style="{DynamicResource GreenButton}" Content="统计" Click="Button_Click" ></Button>
                <Button Style="{DynamicResource BlueButton}" Content="导出" Click="Button_Click"></Button>
            </WrapPanel>
            <Label Content="NG占比分析" Grid.Row="1" Grid.Column="1" Grid.RowSpan="2" FontSize="22" FontWeight="Bold" Foreground="White" HorizontalAlignment="Center" ></Label>
            <lvc:PieChart Name="chart_NG" HoverPushOut="20" Series="{Binding PieData}" Grid.Column="1" Grid.Row="2" Grid.RowSpan="3" LegendLocation="Right" DataTooltip="{x:Null}" InnerRadius="0" FontSize="21" Width="600" Foreground="White">
                <lvc:PieChart.ChartLegend>
                    <lvc:DefaultLegend BulletSize="16"></lvc:DefaultLegend>
                </lvc:PieChart.ChartLegend>
            </lvc:PieChart>
            <Label Content="{Binding Yield}" Grid.Row="5" Grid.Column="1" Grid.RowSpan="2" FontSize="16" FontWeight="Bold" Foreground="White" HorizontalAlignment="Center" ></Label>
        </Grid>
    </Viewbox>
</UserControl>
