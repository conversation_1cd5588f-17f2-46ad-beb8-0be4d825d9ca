﻿using Models;
using System.Diagnostics;
using System.IO;

namespace WorkPieceDataCapture
{
    public static class GlobalClass
    {
        public static Dictionary<string, int> dic_ExportColumnRelation = new Dictionary<string, int>()  //导出明细时字段与Excel中所在列的对应关系
        {
            {"iID",0},
            {"ZC_SerialNo",1},
            {"ZC_Model",2},
            {"ZC_Time",3},
            {"ZC_LaserNo",4},
            {"ZC_Result",5},
            {"FLJ_ZT_Model",6},
            {"FLJ_ZT_Material",7},
            {"FLJ_ZT_Upper",8},
            {"FLJ_ZT_Lower",9},
            {"FLJ_ZT_Value",10},
            {"FLJ_ZT_Result",11},
            {"FLJ_YCQ_Model",12},
            {"FLJ_YCQ_Material",13},
            {"FLJ_GLH_Model",14},
            {"FLJ_GLH_Material",15},
            {"FLJ_GLH_Upper",16},
            {"FLJ_GLH_Lower",17},
            {"FLJ_GLH_Value",18},
            {"FLJ_GLH_Result",19},
            {"FLJ_ECQ_Model",20},
            {"FLJ_ECQ_Material",21},
            {"FLJ_ECQ_Upper",22},
            {"FLJ_ECQ_Lower",23},
            {"FLJ_ECQ_Value",24},
            {"FLJ_ECQ_Result",25},
            {"FLJ_KZH_Model",26},
            {"FLJ_KZH_Material",27},
            {"FLJ_KZH_Upper",28},
            {"FLJ_KZH_Lower",29},
            {"FLJ_KZH_Value",30},
            {"FLJ_KZH_PressureResult",31},
            {"FLJ_KZH_Result",32},
            {"FLJ_SK_Model",33},
            {"FLJ_SK_Material",34},
            {"FLJ_SK_Photo",35},
            {"FLJ_SK_Result",36},
            {"FLJ_WM_Model",37},
            {"FLJ_WM_Material",38},
            {"FLJ_WM_Photo1",39},
            {"FLJ_WM_Photo2",40},
            {"FLJ_WM_Result",41},
            {"JCY_NM_Photo1",42},
            {"JCY_NM_Photo2",43},
            {"JCY_NM_Result",44},
            {"JCY_QM_Program",45},
            {"JCY_QM_Pressure",46},
            {"JCY_QM_Leak",47},
            {"JCY_QM_Result",48}
        };
        public static Dictionary<string, int> dic_ExportDetailRelation = new Dictionary<string, int>()  //导出明细时字段与Excel中所在行的对应关系
        {
            {"ZC_SerialNo",2}, // 总成设备标识码
            {"ZC_Model",3}, // 总成型号
            {"ZC_Time",4}, // 总成生产时间
            {"ZC_LaserNo",5}, // 总成激光打码编码
            {"ZC_Result",6}, // 总成综合结果
            {"FLJ_ZT_Model",7}, // 分零件：主体型号码
            {"FLJ_ZT_Material",8}, // 分零件：主体物料编码
            {"FLJ_ZT_Upper",9}, // 分零件：主体异物位移检测参数上限
            {"FLJ_ZT_Lower",10}, // 分零件：主体异物位移检测参数下限
            {"FLJ_ZT_Value",11}, // 分零件：主体异物位移检测值
            {"FLJ_ZT_Result",12}, // 分零件：主体异物位移检测结果
            {"FLJ_YCQ_Model",13}, // 分零件：一次圈型号码
            {"FLJ_YCQ_Material",14}, // 分零件：一次圈物料编码
            {"FLJ_GLH_Model",15}, // 分零件：隔离环型号码
            {"FLJ_GLH_Material",16}, // 分零件：隔离环物料编码
            {"FLJ_GLH_Upper",17}, // 分零件：隔离环位移检测参数上限
            {"FLJ_GLH_Lower",18}, // 分零件：隔离环位移检测参数下限
            {"FLJ_GLH_Value",19}, // 分零件：隔离环检测值
            {"FLJ_GLH_Result",20}, // 分零件：隔离环检测结果
            {"FLJ_ECQ_Model",21}, // 分零件：二次圈型号码
            {"FLJ_ECQ_Material",22}, // 分零件：二次圈物料编码
            {"FLJ_ECQ_Upper",23}, // 分零件：二次圈位移检测参数上限
            {"FLJ_ECQ_Lower",24}, // 分零件：二次圈位移检测参数下限
            {"FLJ_ECQ_Value",25}, // 分零件：二次圈位移检测值
            {"FLJ_ECQ_Result",26}, // 分零件：二次圈检测结果
            {"FLJ_KZH_Model",27}, // 分零件：安全环型号码
            {"FLJ_KZH_Material",28}, // 分零件：安全环物料编码
            {"FLJ_KZH_Upper",29}, // 分零件：安全环位移检测参数上限
            {"FLJ_KZH_Lower",30}, // 分零件：安全环位移检测参数下限
            {"FLJ_KZH_Value",31}, // 分零件：安全环检测位移偏差
            {"CoordinateData",32}, // 分零件：安全环压力检测值
            {"FLJ_KZH_PressureResult",33}, // 分零件：安全环压力检测结果
            {"FLJ_KZH_Result",34}, // 分零件：安全环检测结果
            {"FLJ_SK_Model",35}, // 分零件：锁扣型号码
            {"FLJ_SK_Material",36}, // 分零件：锁扣物料编码
            {"FLJ_SK_Photo",37}, // 分零件：锁扣相机存储路径
            {"FLJ_SK_Result",38}, // 分零件：锁扣检测结果
            {"FLJ_WM_Model",39 },// 分零件：外密型号码
            {"FLJ_WM_Material",40 },// 分零件：外密物料编码
            {"FLJ_WM_Photo1",41 },// 分零件：外密相机1路径
            {"FLJ_WM_Photo2",42 },// 分零件：外密相机2路径
            {"FLJ_WM_Result",43 },// 分零件：外密检测结果
            {"JCY_NM_Photo1",44}, // 检测仪：内密相机存储路径1
            {"JCY_NM_Photo2",45}, // 检测仪：内密相机存储路径2
            {"JCY_NM_Result",46}, // 检测仪：内密检测结果
            {"JCY_QM_Program",47}, // 检测仪：气密检测程序号
            {"JCY_QM_Pressure",48}, // 检测仪：气密检测压力值
            {"JCY_QM_Leak",49}, // 检测仪：气密检测泄漏值
            {"JCY_QM_Result",50} // 检测仪：气密检测结果
        };
        public static int WindowWidth = 0;
        public static int WindowHeight = 0;
        #region 预留
        //{"FLJ_WM_Model",1}, // 分零件：外密型号码
        //{"FLJ_WM_Material",1}, // 分零件：外密物料编码
        //{"FLJ_WM_Result",1}, // 分零件：外密检测结果
        //{"FLJ_WM_Photo1",1}, // 分零件：外密相机1存储路径
        //{"FLJ_WM_Photo2",1}, // 分零件：外密相机2存储路径
        //{"JCY_KTQM_Result",1}, // 检测仪：壳体气密检测结果
        //{"JCY_KTQM_Program",1}, // 检测仪：壳体气密程序号
        //{"JCY_KTQM_Pressure",1}, // 检测仪：壳体气密压力值
        //{"JCY_KTQM_Leak",1}, // 检测仪：壳体气密泄漏值
        //{"FLJ_LH_Model",1}, // 分零件：拉坏型号码
        //{"FLJ_LH_Material",1}, // 分零件：拉坏物料编码
        //{"FLJ_LH_Result",1}, // 分零件：拉坏检测结果
        //{"FLJ_LH_LaserNo",1}, // 分零件：拉坏激光打码编码
        //{"FLJ_LH_Photo1",1}, // 分零件：拉坏相机1存储路径
        //{"FLJ_LH_Photo2",1}, // 分零件：拉坏相机2存储路径
        #endregion
        public static FuncResult<string> openFile(string strPath)
        {
            var result = new FuncResult<string>();
            try
            {
                if (File.Exists(strPath))
                {
                    Process.Start("explorer.exe", $"/open,\"{strPath}\"");
                    result.bSuccess = true;
                }
                else
                {
                    result.bSuccess = false;
                    result.strMsg = "文件不存在！";
                }
            }
            catch (Exception ex)
            {
                result.bSuccess = false;
                result.strMsg = "文件打开失败！\n" + ex.Message;
            }
            return result;
        }
    }
}
