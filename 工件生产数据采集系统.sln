﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.8.34511.84
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "WorkPieceDataCapture", "WorkPieceDataCapture\WorkPieceDataCapture.csproj", "{F56D17CF-1033-4F44-9857-06B987F396D3}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Models", "Models\Models.csproj", "{48201948-FB1A-49AA-83EB-ACE3E0ED1E78}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Interfaces", "Interfaces\Interfaces.csproj", "{67AE93BB-606D-4EB6-B0AD-6480F80145F2}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Services", "Services\Services.csproj", "{FCEF6A0E-A738-4BBB-9EFE-CA5A05D31E61}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Utils", "Utils\Utils.csproj", "{8E87F655-6114-4524-BDF5-419C55B50881}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Respository", "Respository\Respository.csproj", "{BD292DF5-0BEE-4B2B-8335-3455D0F45C7F}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{F56D17CF-1033-4F44-9857-06B987F396D3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F56D17CF-1033-4F44-9857-06B987F396D3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F56D17CF-1033-4F44-9857-06B987F396D3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F56D17CF-1033-4F44-9857-06B987F396D3}.Release|Any CPU.Build.0 = Release|Any CPU
		{48201948-FB1A-49AA-83EB-ACE3E0ED1E78}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{48201948-FB1A-49AA-83EB-ACE3E0ED1E78}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{48201948-FB1A-49AA-83EB-ACE3E0ED1E78}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{48201948-FB1A-49AA-83EB-ACE3E0ED1E78}.Release|Any CPU.Build.0 = Release|Any CPU
		{67AE93BB-606D-4EB6-B0AD-6480F80145F2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{67AE93BB-606D-4EB6-B0AD-6480F80145F2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{67AE93BB-606D-4EB6-B0AD-6480F80145F2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{67AE93BB-606D-4EB6-B0AD-6480F80145F2}.Release|Any CPU.Build.0 = Release|Any CPU
		{FCEF6A0E-A738-4BBB-9EFE-CA5A05D31E61}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FCEF6A0E-A738-4BBB-9EFE-CA5A05D31E61}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FCEF6A0E-A738-4BBB-9EFE-CA5A05D31E61}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FCEF6A0E-A738-4BBB-9EFE-CA5A05D31E61}.Release|Any CPU.Build.0 = Release|Any CPU
		{8E87F655-6114-4524-BDF5-419C55B50881}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8E87F655-6114-4524-BDF5-419C55B50881}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8E87F655-6114-4524-BDF5-419C55B50881}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8E87F655-6114-4524-BDF5-419C55B50881}.Release|Any CPU.Build.0 = Release|Any CPU
		{BD292DF5-0BEE-4B2B-8335-3455D0F45C7F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BD292DF5-0BEE-4B2B-8335-3455D0F45C7F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BD292DF5-0BEE-4B2B-8335-3455D0F45C7F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BD292DF5-0BEE-4B2B-8335-3455D0F45C7F}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {A374F58A-DEB9-404F-BB7E-58F34C9E0B44}
	EndGlobalSection
EndGlobal
