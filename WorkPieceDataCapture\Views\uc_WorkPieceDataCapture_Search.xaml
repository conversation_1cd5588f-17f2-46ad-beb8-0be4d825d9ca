﻿<UserControl x:Class="WorkPieceDataCapture.Views.uc_WorkPieceDataCapture_Search" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
xmlns:d="http://schemas.microsoft.com/expression/blend/2008" mc:Ignorable="d" d:DesignHeight="1000" d:DesignWidth="1920">
    <Viewbox Stretch="Fill">
        <Grid Style="{DynamicResource GeneralGrid}" Height="1000" Width="1970">
            <Grid.RowDefinitions>
                <RowDefinition Height="40"></RowDefinition>
                <RowDefinition Height="*"></RowDefinition>
            </Grid.RowDefinitions>
            <Button Content="查询" Style="{DynamicResource GreenButton}" Click="Button_Click" Margin="50,0,0,0"></Button>
            <Button Content="导出" Style="{DynamicResource BlueButton}" Click="Button_Click" Margin="135,0,0,0"></Button>
            <Grid Grid.Row="1">
                <Grid.RowDefinitions>
                    <RowDefinition Height="27"></RowDefinition>
                    <RowDefinition Height="50"></RowDefinition>
                    <RowDefinition Height="*"></RowDefinition>
                    <RowDefinition Height="40"></RowDefinition>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="50"></ColumnDefinition>
                    <ColumnDefinition Width="170"></ColumnDefinition>
                    <ColumnDefinition Width="150"></ColumnDefinition>
                    <ColumnDefinition Width="130"></ColumnDefinition>
                    <ColumnDefinition Width="120"></ColumnDefinition>
                    <ColumnDefinition Width="55"></ColumnDefinition>
                    <ColumnDefinition Width="55"></ColumnDefinition>
                    <ColumnDefinition Width="55"></ColumnDefinition>
                    <ColumnDefinition Width="55"></ColumnDefinition>
                    <ColumnDefinition Width="55"></ColumnDefinition>
                    <ColumnDefinition Width="55"></ColumnDefinition>
                    <ColumnDefinition Width="55"></ColumnDefinition>
                    <ColumnDefinition Width="55"></ColumnDefinition>
                    <ColumnDefinition Width="55"></ColumnDefinition>
                    <ColumnDefinition Width="55"></ColumnDefinition>
                    <ColumnDefinition Width="55"></ColumnDefinition>
                    <ColumnDefinition Width="55"></ColumnDefinition>
                    <ColumnDefinition Width="55"></ColumnDefinition>
                    <ColumnDefinition Width="55"></ColumnDefinition>
                    <ColumnDefinition Width="55"></ColumnDefinition>
                    <ColumnDefinition Width="55"></ColumnDefinition>
                    <ColumnDefinition Width="55"></ColumnDefinition>
                    <ColumnDefinition Width="55"></ColumnDefinition>
                    <ColumnDefinition Width="55"></ColumnDefinition>
                    <ColumnDefinition Width="55"></ColumnDefinition>
                    <ColumnDefinition Width="55"></ColumnDefinition>
                    <ColumnDefinition Width="55"></ColumnDefinition>
                    <ColumnDefinition Width="55"></ColumnDefinition>
                    <ColumnDefinition Width="55"></ColumnDefinition>
                    <ColumnDefinition Width="*"></ColumnDefinition>
                </Grid.ColumnDefinitions>
                <!--总成-->
                <Label Content="序号" Grid.Row="0" Grid.RowSpan="2" Margin="5,0,0,0" Style="{DynamicResource TableTitle}"></Label>
                <Label Content="总成" Grid.Row="0" Grid.Column="1" Grid.ColumnSpan="5" Style="{DynamicResource TableTitle}" VerticalContentAlignment="Top"></Label>
                <Label Content="总成设备标识码" Grid.Row="1" Grid.Column="1" Style="{DynamicResource TableTitle}"></Label>
                <Label Content="总成型号" Grid.Row="1" Grid.Column="2" Style="{DynamicResource TableTitle}"></Label>
                <Label Content="生产时间" Grid.Row="1" Grid.Column="3" Style="{DynamicResource TableTitle}"></Label>
                <Label Content="激光打码编码" Grid.Row="1" Grid.Column="4" Style="{DynamicResource TableTitle}"></Label>
                <Label Content="综合&#xA;结果" Grid.Row="1" Grid.Column="5" Style="{DynamicResource TableTitle}"></Label>
                <!--分零件：主体-->
                <Label Content="分零件:主体" Grid.Row="0" Grid.Column="6" Grid.ColumnSpan="2" Style="{DynamicResource TableTitle}"></Label>
                <Label Content="检测&#xA;数值" Grid.Row="1" Grid.Column="6" Style="{DynamicResource TableTitle}"></Label>
                <Label Content="检测&#xA;结果" Grid.Row="1" Grid.Column="7" Style="{DynamicResource TableTitle}"></Label>
                <!--分零件：隔离环-->
                <Label Content="分零件:隔离环" Grid.Row="0" Grid.Column="8" Grid.ColumnSpan="2" Style="{DynamicResource TableTitle}"></Label>
                <Label Content="检测&#xA;数值" Grid.Row="1" Grid.Column="8" Style="{DynamicResource TableTitle}"></Label>
                <Label Content="检测&#xA;结果" Grid.Row="1" Grid.Column="9" Style="{DynamicResource TableTitle}"></Label>
                <!--分零件：二次圈-->
                <Label Content="分零件:二次圈" Grid.Row="0" Grid.Column="10" Grid.ColumnSpan="2" Style="{DynamicResource TableTitle}"></Label>
                <Label Content="检测&#xA;数值" Grid.Row="1" Grid.Column="10" Style="{DynamicResource TableTitle}"></Label>
                <Label Content="检测&#xA;结果" Grid.Row="1" Grid.Column="11" Style="{DynamicResource TableTitle}"></Label>
                <!--分零件：安全环-->
                <Label Content="分零件:安全环" Grid.Row="0" Grid.Column="12" Grid.ColumnSpan="4" Style="{DynamicResource TableTitle}"></Label>
                <Label Content="检测&#xA;数值" Grid.Row="1" Grid.Column="12" Style="{DynamicResource TableTitle}"></Label>
                <Label Content="压力&#xA;曲线" Grid.Row="1" Grid.Column="13" Style="{DynamicResource TableTitle}"></Label>
                <Label Content="压力&#xA;结果" Grid.Row="1" Grid.Column="14" Style="{DynamicResource TableTitle}"></Label>
                <Label Content="检测&#xA;结果" Grid.Row="1" Grid.Column="15" Style="{DynamicResource TableTitle}"></Label>
                <!--分零件：锁扣-->
                <Label Content="分零件:锁扣" Grid.Row="0" Grid.Column="16" Grid.ColumnSpan="2" Style="{DynamicResource TableTitle}"></Label>
                <Label Content="相机&#xA;路径" Grid.Row="1" Grid.Column="16" Style="{DynamicResource TableTitle}"></Label>
                <Label Content="检测&#xA;结果" Grid.Row="1" Grid.Column="17" Style="{DynamicResource TableTitle}"></Label>
                <!--检测仪：内密-->
                <Label Content="检测仪:内密" Grid.Row="0" Grid.Column="18" Grid.ColumnSpan="3" Style="{DynamicResource TableTitle}"></Label>
                <Label Content="相机1&#xA;路径" Grid.Row="1" Grid.Column="18" Style="{DynamicResource TableTitle}"></Label>
                <Label Content="相机2&#xA;路径" Grid.Row="1" Grid.Column="19" Style="{DynamicResource TableTitle}"></Label>
                <Label Content="检测&#xA;结果" Grid.Row="1" Grid.Column="20" Style="{DynamicResource TableTitle}"></Label>
                <!--分零件：外密-->
                <Label Content="分零件:外密" Grid.Row="0" Grid.Column="21" Grid.ColumnSpan="3" Style="{DynamicResource TableTitle}"></Label>
                <Label Content="相机1&#xA;路径" Grid.Row="1" Grid.Column="21" Style="{DynamicResource TableTitle}"></Label>
                <Label Content="相机2&#xA;路径" Grid.Row="1" Grid.Column="22" Style="{DynamicResource TableTitle}"></Label>
                <Label Content="检测&#xA;结果" Grid.Row="1" Grid.Column="23" Style="{DynamicResource TableTitle}"></Label>
                <!--检测仪：内密气密-->
                <Label Content="检测仪:内密气密" Grid.Row="0" Grid.Column="24" Grid.ColumnSpan="4" Style="{DynamicResource TableTitle}"></Label>
                <Label Content="程序&#xA;编号" Grid.Row="1" Grid.Column="24" Style="{DynamicResource TableTitle}"></Label>
                <Label Content="压力&#xA;数值" Grid.Row="1" Grid.Column="25" Style="{DynamicResource TableTitle}"></Label>
                <Label Content="泄漏&#xA;数值" Grid.Row="1" Grid.Column="26" Style="{DynamicResource TableTitle}"></Label>
                <Label Content="检测&#xA;结果" Grid.Row="1" Grid.Column="27" Style="{DynamicResource TableTitle}"></Label>
                <!--操作-->
                <Label Content="操作" Grid.Row="0" Grid.RowSpan="2" Grid.Column="28" Style="{DynamicResource TableTitle}"></Label>
                <Label Margin="0,0,5,0" Grid.Row="0" Grid.RowSpan="2" Grid.ColumnSpan="3" Grid.Column="29" Style="{DynamicResource TableTitle}"></Label>
                <DataGrid Name="GridProduceRecord" Background="#015478" Grid.Row="2" Grid.ColumnSpan="100"  ItemsSource="{Binding list_History}" Margin="5,0,5,0" HeadersVisibility="None" SelectionMode="Single" IsReadOnly="True"  PreviewMouseDoubleClick="GridProduceRecord_PreviewMouseDoubleClick" PreviewMouseDown="GridProduceRecord_PreviewMouseDown" CanUserAddRows="False" CanUserDeleteRows="False" CanUserResizeColumns="False" CanUserSortColumns="False" AutoGenerateColumns="False" AllowDrop="False" RowBackground="#015478" >
                    <DataGrid.Resources>
                        <Style TargetType="DataGridCell">
                            <Setter Property="Height" Value="28"></Setter>
                            <Setter Property="BorderThickness" Value="0"></Setter>
                            <Setter Property="BorderBrush" Value="#015478"></Setter>
                            <Setter Property="Margin" Value="-1"></Setter>
                            <Style.Triggers>
                                <Trigger Property="IsSelected" Value="False">
                                    <Setter Property="Background" Value="#015478"/>
                                </Trigger>
                            </Style.Triggers>
                        </Style>
                        <Style TargetType="DataGridRow">
                            <Setter Property="Margin" Value="-1"></Setter>
                        </Style>
                        <Style TargetType="{x:Type DataGridColumnHeader}">
                            <Setter Property="Background" Value="#027DB4"/>
                            <Setter Property="Foreground" Value="White"/>
                            <Setter Property="FontWeight" Value="Bold"/>
                            <Setter Property="Height" Value="34"/>
                            <Setter Property="FontSize" Value="12"/>
                            <Setter Property="HorizontalContentAlignment" Value="Center"/>
                        </Style>
                    </DataGrid.Resources>
                    <DataGrid.Columns>
                        <DataGridTemplateColumn Width="45" Header="序号">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBlock Text="{Binding iID}"  Style="{DynamicResource TableTestValue}" HorizontalAlignment="Center"></TextBlock>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                        <DataGridTemplateColumn Width="170" Header="总成设备标识码">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBlock Text="{Binding ZC_SerialNo}"  Style="{DynamicResource TableTestValue}"></TextBlock>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                        <DataGridTemplateColumn Width="150" Header="总成型号">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBlock Text="{Binding ZC_Model}"  Style="{DynamicResource TableTestValue}"></TextBlock>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                        <DataGridTemplateColumn Width="130" Header="生产时间">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBlock Text="{Binding ZC_Time, StringFormat='yyyyMMddHHmm'}"  Style="{DynamicResource TableTestValue}"></TextBlock>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                        <DataGridTemplateColumn Width="120" Header="激光打码编码">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBlock Text="{Binding ZC_LaserNo}"  Style="{DynamicResource TableTestValue}"></TextBlock>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                        <DataGridTemplateColumn Width="55" Header="综合结果">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBlock FontWeight="Bold" FontSize="17" HorizontalAlignment="Center" VerticalAlignment="Center">
                                        <TextBlock.Style>
                                            <Style TargetType="{x:Type TextBlock}">
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding ZC_Result}" Value="OK">
                                                        <Setter Property="Foreground" Value="#00FFFF" />
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding ZC_Result}" Value="NG">
                                                        <Setter Property="Foreground" Value="Red" />
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding ZC_Result}" Value="NA">
                                                        <Setter Property="Foreground" Value="Gray" />
                                                    </DataTrigger>
                                                    <!-- 其他触发器 -->
                                                </Style.Triggers>
                                            </Style>
                                        </TextBlock.Style>
                          <Run Text="{Binding ZC_Result}" />
                                    </TextBlock>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                        <DataGridTemplateColumn Width="55" Header="分零件主体检测值">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBlock Text="{Binding FLJ_ZT_Value}" Style="{DynamicResource TableTestValue}"></TextBlock>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                        <DataGridTemplateColumn Width="55" Header="分零件主体检测结果">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBlock FontWeight="Bold" FontSize="17" HorizontalAlignment="Center" VerticalAlignment="Center">
                                        <TextBlock.Style>
                                            <Style TargetType="{x:Type TextBlock}">
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding FLJ_ZT_Result}" Value="OK">
                                                        <Setter Property="Foreground" Value="#00FFFF" />
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding FLJ_ZT_Result}" Value="NG">
                                                        <Setter Property="Foreground" Value="Red" />
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding FLJ_ZT_Result}" Value="NA">
                                                        <Setter Property="Foreground" Value="Gray" />
                                                    </DataTrigger>
                                                    <!-- 其他触发器 -->
                                                </Style.Triggers>
                                            </Style>
                                        </TextBlock.Style>
                                    <Run Text="{Binding FLJ_ZT_Result}" />
                                    </TextBlock>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                        <DataGridTemplateColumn Width="55" Header="分零件隔离环检测值">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBlock Text="{Binding FLJ_GLH_Value}"  Style="{DynamicResource TableTestValue}"></TextBlock>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                        <DataGridTemplateColumn Width="55" Header="分零件隔离环检测结果">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBlock FontWeight="Bold" FontSize="17" HorizontalAlignment="Center" VerticalAlignment="Center">
                                        <TextBlock.Style>
                                            <Style TargetType="{x:Type TextBlock}">
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding FLJ_GLH_Result}" Value="OK">
                                                        <Setter Property="Foreground" Value="#00FFFF" />
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding FLJ_GLH_Result}" Value="NG">
                                                        <Setter Property="Foreground" Value="Red" />
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding FLJ_GLH_Result}" Value="NA">
                                                        <Setter Property="Foreground" Value="Gray" />
                                                    </DataTrigger>
                                                    <!-- 其他触发器 -->
                                                </Style.Triggers>
                                            </Style>
                                        </TextBlock.Style>
                                    <Run Text="{Binding FLJ_GLH_Result}" />
                                    </TextBlock>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                        <DataGridTemplateColumn Width="55" Header="分零件二次圈检测值">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBlock Text="{Binding FLJ_ECQ_Value}"  Style="{DynamicResource TableTestValue}"></TextBlock>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                        <DataGridTemplateColumn Width="55" Header="分零件二次圈检测结果">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBlock FontWeight="Bold" FontSize="17" HorizontalAlignment="Center" VerticalAlignment="Center">
                                        <TextBlock.Style>
                                            <Style TargetType="{x:Type TextBlock}">
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding FLJ_ECQ_Result}" Value="OK">
                                                        <Setter Property="Foreground" Value="#00FFFF" />
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding FLJ_ECQ_Result}" Value="NG">
                                                        <Setter Property="Foreground" Value="Red" />
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding FLJ_ECQ_Result}" Value="NA">
                                                        <Setter Property="Foreground" Value="Gray" />
                                                    </DataTrigger>
                                                    <!-- 其他触发器 -->
                                                </Style.Triggers>
                                            </Style>
                                        </TextBlock.Style>
                                    <Run Text="{Binding FLJ_ECQ_Result}" />
                                    </TextBlock>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                        <DataGridTemplateColumn Width="55" Header="分零件安全环检测值">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBlock Text="{Binding FLJ_KZH_Value}"  Style="{DynamicResource TableTestValue}"></TextBlock>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                        <DataGridTemplateColumn Width="55" Header="分零件锁扣相机路径">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Button Tag="YLQX" Content="查看" Style="{DynamicResource FileLinkButton}" HorizontalAlignment="Center" Click="Button_Click"></Button>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                        <DataGridTemplateColumn Width="55" Header="分零件安全环压力检测结果">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBlock FontWeight="Bold" FontSize="17" HorizontalAlignment="Center" VerticalAlignment="Center">
                                        <TextBlock.Style>
                                            <Style TargetType="{x:Type TextBlock}">
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding FLJ_KZH_PressureResult}" Value="OK">
                                                        <Setter Property="Foreground" Value="#00FFFF" />
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding FLJ_KZH_PressureResult}" Value="NG">
                                                        <Setter Property="Foreground" Value="Red" />
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding FLJ_KZH_PressureResult}" Value="NA">
                                                        <Setter Property="Foreground" Value="Gray" />
                                                    </DataTrigger>
                                                    <!-- 其他触发器 -->
                                                </Style.Triggers>
                                            </Style>
                                        </TextBlock.Style>
                                    <Run Text="{Binding FLJ_KZH_PressureResult}" />
                                    </TextBlock>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                        <DataGridTemplateColumn Width="55" Header="分零件安全环检测结果">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBlock FontWeight="Bold" FontSize="17" HorizontalAlignment="Center" VerticalAlignment="Center">
                                        <TextBlock.Style>
                                            <Style TargetType="{x:Type TextBlock}">
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding FLJ_KZH_Result}" Value="OK">
                                                        <Setter Property="Foreground" Value="#00FFFF" />
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding FLJ_KZH_Result}" Value="NG">
                                                        <Setter Property="Foreground" Value="Red" />
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding FLJ_KZH_Result}" Value="NA">
                                                        <Setter Property="Foreground" Value="Gray" />
                                                    </DataTrigger>
                                                    <!-- 其他触发器 -->
                                                </Style.Triggers>
                                            </Style>
                                        </TextBlock.Style>
                                    <Run Text="{Binding FLJ_KZH_Result}" />
                                    </TextBlock>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                        <DataGridTemplateColumn Width="55" Header="分零件锁扣相机路径">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Button Tag="{Binding FLJ_SK_Photo}" Content="查看" Style="{DynamicResource FileLinkButton}" HorizontalAlignment="Center" Click="Button_Click"></Button>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                        <DataGridTemplateColumn Width="55" Header="分零件锁扣检测结果">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBlock FontWeight="Bold" FontSize="17" HorizontalAlignment="Center" VerticalAlignment="Center">
                                        <TextBlock.Style>
                                            <Style TargetType="{x:Type TextBlock}">
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding FLJ_SK_Result}" Value="OK">
                                                        <Setter Property="Foreground" Value="#00FFFF" />
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding FLJ_SK_Result}" Value="NG">
                                                        <Setter Property="Foreground" Value="Red" />
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding FLJ_SK_Result}" Value="NA">
                                                        <Setter Property="Foreground" Value="Gray" />
                                                    </DataTrigger>
                                                    <!-- 其他触发器 -->
                                                </Style.Triggers>
                                            </Style>
                                        </TextBlock.Style>
                                    <Run Text="{Binding FLJ_SK_Result}" />
                                    </TextBlock>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                        <DataGridTemplateColumn Width="55" Header="分零件内密相机1路径">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Button Tag="{Binding JCY_NM_Photo1}" Content="查看" Style="{DynamicResource FileLinkButton}" HorizontalAlignment="Center" Click="Button_Click"></Button>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                        <DataGridTemplateColumn Width="55" Header="分零件内密相机2路径">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Button Tag="{Binding JCY_NM_Photo2}" Content="查看" Style="{DynamicResource FileLinkButton}" HorizontalAlignment="Center" Click="Button_Click"></Button>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                        <DataGridTemplateColumn Width="55" Header="分零件内密检测结果">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBlock FontWeight="Bold" FontSize="17" HorizontalAlignment="Center" VerticalAlignment="Center">
                                        <TextBlock.Style>
                                            <Style TargetType="{x:Type TextBlock}">
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding JCY_NM_Result}" Value="OK">
                                                        <Setter Property="Foreground" Value="#00FFFF" />
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding JCY_NM_Result}" Value="NG">
                                                        <Setter Property="Foreground" Value="Red" />
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding JCY_NM_Result}" Value="NA">
                                                        <Setter Property="Foreground" Value="Gray" />
                                                    </DataTrigger>
                                                    <!-- 其他触发器 -->
                                                </Style.Triggers>
                                            </Style>
                                        </TextBlock.Style>
                                    <Run Text="{Binding JCY_NM_Result}" />
                                    </TextBlock>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                        <DataGridTemplateColumn Width="55" Header="分零件外密相机1路径">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Button Tag="{Binding FLJ_WM_Photo1}" Content="查看" Style="{DynamicResource FileLinkButton}" HorizontalAlignment="Center" Click="Button_Click"></Button>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                        <DataGridTemplateColumn Width="55" Header="分零件外密相机2路径">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Button Tag="{Binding FLJ_WM_Photo2}" Content="查看" Style="{DynamicResource FileLinkButton}" HorizontalAlignment="Center" Click="Button_Click"></Button>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                        <DataGridTemplateColumn Width="55" Header="分零件外密检测结果">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBlock FontWeight="Bold" FontSize="17" HorizontalAlignment="Center" VerticalAlignment="Center">
                                        <TextBlock.Style>
                                            <Style TargetType="{x:Type TextBlock}">
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding FLJ_WM_Result}" Value="OK">
                                                        <Setter Property="Foreground" Value="#00FFFF" />
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding FLJ_WM_Result}" Value="NG">
                                                        <Setter Property="Foreground" Value="Red" />
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding FLJ_WM_Result}" Value="NA">
                                                        <Setter Property="Foreground" Value="Gray" />
                                                    </DataTrigger>
                                                    <!-- 其他触发器 -->
                                                </Style.Triggers>
                                            </Style>
                                        </TextBlock.Style>
                                    <Run Text="{Binding FLJ_WM_Result}" />
                                    </TextBlock>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                        <DataGridTemplateColumn Width="55" Header="检测仪气密程序号">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBlock Text="{Binding JCY_QM_Program}"  Style="{DynamicResource TableTestValue}"></TextBlock>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                        <DataGridTemplateColumn Width="55" Header="检测仪气密压力值">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBlock Text="{Binding JCY_QM_Pressure}"  Style="{DynamicResource TableTestValue}"></TextBlock>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                        <DataGridTemplateColumn Width="55" Header="检测仪气密泄漏值">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBlock Text="{Binding JCY_QM_Leak}"  Style="{DynamicResource TableTestValue}"></TextBlock>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                        <DataGridTemplateColumn Width="55" Header="检测仪气密检测结果">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBlock FontWeight="Bold" FontSize="17" HorizontalAlignment="Center" VerticalAlignment="Center">
                                        <TextBlock.Style>
                                            <Style TargetType="{x:Type TextBlock}">
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding JCY_QM_Result}" Value="OK">
                                                        <Setter Property="Foreground" Value="#00FFFF" />
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding JCY_QM_Result}" Value="NG">
                                                        <Setter Property="Foreground" Value="Red" />
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding JCY_QM_Result}" Value="NA">
                                                        <Setter Property="Foreground" Value="Gray" />
                                                    </DataTrigger>
                                                    <!-- 其他触发器 -->
                                                </Style.Triggers>
                                            </Style>
                                        </TextBlock.Style>
                                    <Run Text="{Binding JCY_QM_Result}" />
                                    </TextBlock>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                        <DataGridTemplateColumn Width="55" Header="操作">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Button FontSize="14" Foreground="LightBlue"  Background="Transparent" Click="Export_Click"  >
                                        <TextBlock TextDecorations="Underline" Text="导出"></TextBlock>
                                    </Button>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>
                <WrapPanel Grid.Row="3" Grid.ColumnSpan="100" Margin="20,0,0,0" >
                    <TextBlock Text="记录总数：" Grid.Row="3" Grid.Column="3" Style="{DynamicResource TableTestValue}"></TextBlock>
                    <TextBlock Text="{Binding searchSummary.RecordCount}" Grid.Row="3" Grid.Column="5" Style="{DynamicResource TableTestValue}"></TextBlock>
                    <TextBlock Text="；" Grid.Row="3" Grid.Column="5" Style="{DynamicResource TableTestValue}"></TextBlock>
                    <TextBlock Text="OK" Grid.Row="3" Grid.Column="5" Style="{DynamicResource TableTestValue}" Foreground="#00FFFF"></TextBlock>
                    <TextBlock Text="/" Grid.Row="3" Grid.Column="5" Style="{DynamicResource TableTestValue}"></TextBlock>
                    <TextBlock Text="NG" Grid.Row="3" Grid.Column="5" Style="{DynamicResource TableTestValue}" Foreground="Orange"></TextBlock>
                    <TextBlock Text="/" Grid.Row="3" Grid.Column="5" Style="{DynamicResource TableTestValue}"></TextBlock>
                    <TextBlock Text="OK%" Grid.Row="3" Grid.Column="5" Style="{DynamicResource TableTestValue}" Foreground="LightGreen"></TextBlock>
                    <TextBlock Text="：" Grid.Row="3" Grid.Column="5" Style="{DynamicResource TableTestValue}"></TextBlock>
                    <WrapPanel>
                        <TextBlock Text="总成" Grid.Row="3" Grid.Column="3" Style="{DynamicResource TableTestValue}"></TextBlock>
                        <TextBlock Text="{Binding searchSummary.ZC_OK}" ToolTip="OK/NG" Grid.Row="3" Grid.Column="5" Style="{DynamicResource TableTestValue}" Foreground="#00FFFF"></TextBlock>
                        <TextBlock Text="/" ToolTip="OK/NG" Grid.Row="3" Grid.Column="5" Style="{DynamicResource TableTestValue}"></TextBlock>
                        <TextBlock Text="{Binding searchSummary.ZC_NG}" ToolTip="OK/NG" Grid.Row="3" Grid.Column="5" Style="{DynamicResource TableTestValue}" Foreground="Orange"></TextBlock>
                        <TextBlock Text="/" ToolTip="OK/NG" Grid.Row="3" Grid.Column="5" Style="{DynamicResource TableTestValue}"></TextBlock>
                        <TextBlock Text="{Binding searchSummary.ZC_Percent}" ToolTip="OK/NG" Grid.Row="3" Grid.Column="5" Style="{DynamicResource TableTestValue}" Foreground="LightGreen"></TextBlock>
                        <TextBlock Text="  " ToolTip="OK/NG" Grid.Row="3" Grid.Column="5" Style="{DynamicResource TableTestValue}"></TextBlock>
                    </WrapPanel>
                    <WrapPanel>
                        <TextBlock Text="主体" Grid.Row="3" Grid.Column="3" Style="{DynamicResource TableTestValue}"></TextBlock>
                        <TextBlock Text="{Binding searchSummary.ZT_OK}" ToolTip="OK/NG" Grid.Row="3" Grid.Column="5" Style="{DynamicResource TableTestValue}" Foreground="#00FFFF"></TextBlock>
                        <TextBlock Text="/" ToolTip="OK/NG" Grid.Row="3" Grid.Column="5" Style="{DynamicResource TableTestValue}"></TextBlock>
                        <TextBlock Text="{Binding searchSummary.ZT_NG}" ToolTip="OK/NG" Grid.Row="3" Grid.Column="5" Style="{DynamicResource TableTestValue}" Foreground="Orange"></TextBlock>
                        <TextBlock Text="/" ToolTip="OK/NG" Grid.Row="3" Grid.Column="5" Style="{DynamicResource TableTestValue}"></TextBlock>
                        <TextBlock Text="{Binding searchSummary.ZT_Percent}" ToolTip="OK/NG" Grid.Row="3" Grid.Column="5" Style="{DynamicResource TableTestValue}" Foreground="LightGreen"></TextBlock>
                        <TextBlock Text="  " ToolTip="OK/NG" Grid.Row="3" Grid.Column="5" Style="{DynamicResource TableTestValue}"></TextBlock>
                    </WrapPanel>
                    <WrapPanel>
                        <TextBlock Text="隔离环" Grid.Row="3" Grid.Column="3" Style="{DynamicResource TableTestValue}"></TextBlock>
                        <TextBlock Text="{Binding searchSummary.GLH_OK}" ToolTip="OK/NG" Grid.Row="3" Grid.Column="5" Style="{DynamicResource TableTestValue}" Foreground="#00FFFF"></TextBlock>
                        <TextBlock Text="/" ToolTip="OK/NG" Grid.Row="3" Grid.Column="5" Style="{DynamicResource TableTestValue}"></TextBlock>
                        <TextBlock Text="{Binding searchSummary.GLH_NG}" ToolTip="OK/NG" Grid.Row="3" Grid.Column="5" Style="{DynamicResource TableTestValue}" Foreground="Orange"></TextBlock>
                        <TextBlock Text="/" ToolTip="OK/NG" Grid.Row="3" Grid.Column="5" Style="{DynamicResource TableTestValue}"></TextBlock>
                        <TextBlock Text="{Binding searchSummary.GLH_Percent}" ToolTip="OK/NG" Grid.Row="3" Grid.Column="5" Style="{DynamicResource TableTestValue}" Foreground="LightGreen"></TextBlock>
                        <TextBlock Text="  " ToolTip="OK/NG" Grid.Row="3" Grid.Column="5" Style="{DynamicResource TableTestValue}"></TextBlock>
                    </WrapPanel>
                    <WrapPanel>
                        <TextBlock Text="二次圈" Grid.Row="3" Grid.Column="3" Style="{DynamicResource TableTestValue}"></TextBlock>
                        <TextBlock Text="{Binding searchSummary.ECQ_OK}" ToolTip="OK/NG" Grid.Row="3" Grid.Column="5" Style="{DynamicResource TableTestValue}" Foreground="#00FFFF"></TextBlock>
                        <TextBlock Text="/" ToolTip="OK/NG" Grid.Row="3" Grid.Column="5" Style="{DynamicResource TableTestValue}"></TextBlock>
                        <TextBlock Text="{Binding searchSummary.ECQ_NG}" ToolTip="OK/NG" Grid.Row="3" Grid.Column="5" Style="{DynamicResource TableTestValue}" Foreground="Orange"></TextBlock>
                        <TextBlock Text="/" ToolTip="OK/NG" Grid.Row="3" Grid.Column="5" Style="{DynamicResource TableTestValue}"></TextBlock>
                        <TextBlock Text="{Binding searchSummary.ZC_Percent}" ToolTip="OK/NG" Grid.Row="3" Grid.Column="5" Style="{DynamicResource TableTestValue}" Foreground="LightGreen"></TextBlock>
                        <TextBlock Text="  " ToolTip="OK/NG" Grid.Row="3" Grid.Column="5" Style="{DynamicResource TableTestValue}"></TextBlock>
                    </WrapPanel>
                    <WrapPanel>
                        <TextBlock Text="安全环" Grid.Row="3" Grid.Column="3" Style="{DynamicResource TableTestValue}"></TextBlock>
                        <TextBlock Text="{Binding searchSummary.KZH_OK}" ToolTip="OK/NG" Grid.Row="3" Grid.Column="5" Style="{DynamicResource TableTestValue}" Foreground="#00FFFF"></TextBlock>
                        <TextBlock Text="/" ToolTip="OK/NG" Grid.Row="3" Grid.Column="5" Style="{DynamicResource TableTestValue}"></TextBlock>
                        <TextBlock Text="{Binding searchSummary.KZH_NG}" ToolTip="OK/NG" Grid.Row="3" Grid.Column="5" Style="{DynamicResource TableTestValue}" Foreground="Orange"></TextBlock>
                        <TextBlock Text="/" ToolTip="OK/NG" Grid.Row="3" Grid.Column="5" Style="{DynamicResource TableTestValue}"></TextBlock>
                        <TextBlock Text="{Binding searchSummary.KZH_Percent}" ToolTip="OK/NG" Grid.Row="3" Grid.Column="5" Style="{DynamicResource TableTestValue}" Foreground="LightGreen"></TextBlock>
                        <TextBlock Text="  " ToolTip="OK/NG" Grid.Row="3" Grid.Column="5" Style="{DynamicResource TableTestValue}"></TextBlock>
                    </WrapPanel>
                    <WrapPanel>
                        <TextBlock Text="锁扣" Grid.Row="3" Grid.Column="3" Style="{DynamicResource TableTestValue}"></TextBlock>
                        <TextBlock Text="{Binding searchSummary.SK_OK}" ToolTip="OK/NG" Grid.Row="3" Grid.Column="5" Style="{DynamicResource TableTestValue}" Foreground="#00FFFF"></TextBlock>
                        <TextBlock Text="/" ToolTip="OK/NG" Grid.Row="3" Grid.Column="5" Style="{DynamicResource TableTestValue}"></TextBlock>
                        <TextBlock Text="{Binding searchSummary.SK_NG}" ToolTip="OK/NG" Grid.Row="3" Grid.Column="5" Style="{DynamicResource TableTestValue}" Foreground="Orange"></TextBlock>
                        <TextBlock Text="/" ToolTip="OK/NG" Grid.Row="3" Grid.Column="5" Style="{DynamicResource TableTestValue}"></TextBlock>
                        <TextBlock Text="{Binding searchSummary.SK_Percent}" ToolTip="OK/NG" Grid.Row="3" Grid.Column="5" Style="{DynamicResource TableTestValue}" Foreground="LightGreen"></TextBlock>
                        <TextBlock Text="  " ToolTip="OK/NG" Grid.Row="3" Grid.Column="5" Style="{DynamicResource TableTestValue}"></TextBlock>
                    </WrapPanel>
                    <WrapPanel>
                        <TextBlock Text="内密" Grid.Row="3" Grid.Column="3" Style="{DynamicResource TableTestValue}"></TextBlock>
                        <TextBlock Text="{Binding searchSummary.NM_OK}" ToolTip="OK/NG" Grid.Row="3" Grid.Column="5" Style="{DynamicResource TableTestValue}" Foreground="#00FFFF"></TextBlock>
                        <TextBlock Text="/" ToolTip="OK/NG" Grid.Row="3" Grid.Column="5" Style="{DynamicResource TableTestValue}"></TextBlock>
                        <TextBlock Text="{Binding searchSummary.NM_NG}" ToolTip="OK/NG" Grid.Row="3" Grid.Column="5" Style="{DynamicResource TableTestValue}" Foreground="Orange"></TextBlock>
                        <TextBlock Text="/" ToolTip="OK/NG" Grid.Row="3" Grid.Column="5" Style="{DynamicResource TableTestValue}"></TextBlock>
                        <TextBlock Text="{Binding searchSummary.NM_Percent}" ToolTip="OK/NG" Grid.Row="3" Grid.Column="5" Style="{DynamicResource TableTestValue}" Foreground="LightGreen"></TextBlock>
                        <TextBlock Text="  " ToolTip="OK/NG" Grid.Row="3" Grid.Column="5" Style="{DynamicResource TableTestValue}"></TextBlock>
                    </WrapPanel>
                    <WrapPanel>
                        <TextBlock Text="外密" Grid.Row="3" Grid.Column="3" Style="{DynamicResource TableTestValue}"></TextBlock>
                        <TextBlock Text="{Binding searchSummary.WM_OK}" ToolTip="OK/NG" Grid.Row="3" Grid.Column="5" Style="{DynamicResource TableTestValue}" Foreground="#00FFFF"></TextBlock>
                        <TextBlock Text="/" ToolTip="OK/NG" Grid.Row="3" Grid.Column="5" Style="{DynamicResource TableTestValue}"></TextBlock>
                        <TextBlock Text="{Binding searchSummary.WM_NG}" ToolTip="OK/NG" Grid.Row="3" Grid.Column="5" Style="{DynamicResource TableTestValue}" Foreground="Orange"></TextBlock>
                        <TextBlock Text="/" ToolTip="OK/NG" Grid.Row="3" Grid.Column="5" Style="{DynamicResource TableTestValue}"></TextBlock>
                        <TextBlock Text="{Binding searchSummary.WM_Percent}" ToolTip="OK/NG" Grid.Row="3" Grid.Column="5" Style="{DynamicResource TableTestValue}" Foreground="LightGreen"></TextBlock>
                        <TextBlock Text="  " ToolTip="OK/NG" Grid.Row="3" Grid.Column="5" Style="{DynamicResource TableTestValue}"></TextBlock>
                    </WrapPanel>
                    <WrapPanel>
                        <TextBlock Text="内密气密" Grid.Row="3" Grid.Column="3" Style="{DynamicResource TableTestValue}"></TextBlock>
                        <TextBlock Text="{Binding searchSummary.QM_OK}" ToolTip="OK/NG" Grid.Row="3" Grid.Column="5" Style="{DynamicResource TableTestValue}" Foreground="#00FFFF"></TextBlock>
                        <TextBlock Text="/" ToolTip="OK/NG" Grid.Row="3" Grid.Column="5" Style="{DynamicResource TableTestValue}"></TextBlock>
                        <TextBlock Text="{Binding searchSummary.QM_NG}" ToolTip="OK/NG" Grid.Row="3" Grid.Column="5" Style="{DynamicResource TableTestValue}" Foreground="Orange"></TextBlock>
                        <TextBlock Text="/" ToolTip="OK/NG" Grid.Row="3" Grid.Column="5" Style="{DynamicResource TableTestValue}"></TextBlock>
                        <TextBlock Text="{Binding searchSummary.QM_Percent}" ToolTip="OK/NG" Grid.Row="3" Grid.Column="5" Style="{DynamicResource TableTestValue}" Foreground="LightGreen"></TextBlock>
                    </WrapPanel>
                </WrapPanel>
            </Grid>
        </Grid>
    </Viewbox>
</UserControl>
