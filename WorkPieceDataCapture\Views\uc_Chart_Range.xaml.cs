﻿using LiveCharts.Defaults;
using LiveCharts.Wpf;
using LiveCharts;
using System.ComponentModel;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Input;
using System.Windows.Media;
using ViewModels;
using WorkPieceDataCapture.ViewModels;
using Microsoft.Win32;
using System.Drawing.Imaging;
using System.Drawing;

namespace WorkPieceDataCapture.Views
{
    /// <summary>
    /// uc_Chart_NG.xaml 的交互逻辑
    /// </summary>
    public partial class uc_Chart_Range : UserControl
    {
        private string[]? _Labels;

        public event PropertyChangedEventHandler? PropertyChanged;
        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
        public string[]? Labels
        {
            get
            {
                return _Labels;
            }
            set
            {
                _Labels = value;
                OnPropertyChanged(nameof(Labels));
            }
        }
        public uc_Chart_Range()
        {
            InitializeComponent();
            aaa.DataTooltip.Background = new SolidColorBrush(Colors.Black);
        }

        private void Button_Click(object sender, RoutedEventArgs e)
        {
            var btn = sender as Button;
            switch (btn.Content.ToString())
            {
                case "统计":
                    var vm_Summary = this.DataContext as vm_Chart_Range;
                    if (vm_Summary != null && vm_Summary.doSummaryCommand.CanExecute(null))
                    {
                        vm_Summary.doSummaryCommand.Execute(null);
                    }
                    break;
                case "导出":
                    try
                    {
                        // 获取屏幕的大小
                        System.Drawing.Rectangle screenSize = new Rectangle(0, 0, GlobalClass.WindowWidth, GlobalClass.WindowHeight);
                        // 创建一个与屏幕大小相同的位图
                        using (Bitmap bitmap = new Bitmap(screenSize.Width, screenSize.Height))
                        {
                            // 从Graphics对象中获取屏幕截图
                            using (Graphics g = Graphics.FromImage(bitmap))
                            {
                                g.CopyFromScreen(0, 0, 0, 0, screenSize.Size);
                            }
                            SaveFileDialog saveFileDialog = new SaveFileDialog();
                            saveFileDialog.ShowDialog();
                            if (saveFileDialog.FileName != "")
                            {
                                // 保存位图为文件，这里保存为PNG格式
                                string filePath = saveFileDialog.FileName + ".png";
                                bitmap.Save(filePath, ImageFormat.Png);
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show("导出出错！" + ex.Message);
                    }
                    break;
                default:
                    break;
            }
            this.begin.Focus();
        }

        private void Calendar_PreviewMouseUp(object sender, MouseButtonEventArgs e)
        {
            if (Mouse.Captured is CalendarItem)
            {
                Mouse.Capture(null);
            }
        }
   
    }
}
