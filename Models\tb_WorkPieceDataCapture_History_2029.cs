﻿using Interfaces.Model;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Models
{
    public class tb_WorkPieceDataCapture_History_2029 : IDataModel
    {
        //主键
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int? iID { get; set; } // 自增ID
        [Key]
        public string? ZC_SerialNo { get; set; } // 总成设备标识码
        public string? ZC_Model { get; set; } // 总成型号
        public string? ZC_Time { get; set; } // 总成生产时间
        public string? ZC_LaserNo { get; set; } // 总成激光打码编码
        public string? ZC_Result { get; set; } // 总成综合结果
        public string? FLJ_ZT_Model { get; set; } // 分零件：主体型号码
        public string? FLJ_ZT_Material { get; set; } // 分零件：主体物料编码
        public string? FLJ_ZT_Result { get; set; } // 分零件：主体异物位移检测结果
        public double? FLJ_ZT_Value { get; set; } // 分零件：主体异物位移检测值
        public double? FLJ_ZT_Upper { get; set; } // 分零件：主体异物位移检测参数上限
        public double? FLJ_ZT_Lower { get; set; } // 分零件：主体异物位移检测参数下限
        public string? FLJ_YCQ_Model { get; set; } // 分零件：一次圈型号码
        public string? FLJ_YCQ_Material { get; set; } // 分零件：一次圈物料编码
        public string? FLJ_YCQ_Result { get; set; } // 分零件：一次圈检测结果
        public double? FLJ_YCQ_Value { get; set; } // 分零件：一次圈位移检测值
        public double? FLJ_YCQ_Upper { get; set; } // 分零件：一次圈位移检测参数上限
        public double? FLJ_YCQ_Lower { get; set; } // 分零件：一次圈位移检测参数下限
        public string? FLJ_GLH_Model { get; set; } // 分零件：隔离环型号码
        public string? FLJ_GLH_Material { get; set; } // 分零件：隔离环物料编码
        public string? FLJ_GLH_Result { get; set; } // 分零件：隔离环检测结果
        public double? FLJ_GLH_Value { get; set; } // 分零件：隔离环检测值
        public double? FLJ_GLH_Upper { get; set; } // 分零件：隔离环位移检测参数上限
        public double? FLJ_GLH_Lower { get; set; } // 分零件：隔离环位移检测参数下限
        public string? FLJ_ECQ_Model { get; set; } // 分零件：二次圈型号码
        public string? FLJ_ECQ_Material { get; set; } // 分零件：二次圈物料编码
        public string? FLJ_ECQ_Result { get; set; } // 分零件：二次圈检测结果
        public double? FLJ_ECQ_Value { get; set; } // 分零件：二次圈位移检测值
        public double? FLJ_ECQ_Upper { get; set; } // 分零件：二次圈位移检测参数上限
        public double? FLJ_ECQ_Lower { get; set; } // 分零件：二次圈位移检测参数下限
        public string? FLJ_KZH_Model { get; set; } // 分零件：安全环型号码
        public string? FLJ_KZH_Material { get; set; } // 分零件：安全环物料编码
        public string? FLJ_KZH_Result { get; set; } // 分零件：安全环检测结果
        public double? FLJ_KZH_Value { get; set; } // 分零件：安全环检测位移偏差
        public double? FLJ_KZH_Upper { get; set; } // 分零件：安全环位移检测参数上限
        public double? FLJ_KZH_Lower { get; set; } // 分零件：安全环位移检测参数下限
        public string? FLJ_KZH_PressureResult { get; set; } // 分零件：安全环压力检测结果
        public string? FLJ_WM_Model { get; set; } // 分零件：外密型号码
        public string? FLJ_WM_Material { get; set; } // 分零件：外密物料编码
        public string? FLJ_WM_Result { get; set; } // 分零件：外密检测结果
        public string? FLJ_WM_Photo1 { get; set; } // 分零件：外密相机1存储路径
        public string? FLJ_WM_Photo2 { get; set; } // 分零件：外密相机2存储路径
        public string? FLJ_SK_Model { get; set; } // 分零件：锁扣型号码
        public string? FLJ_SK_Material { get; set; } // 分零件：锁扣物料编码
        public string? FLJ_SK_Result { get; set; } // 分零件：锁扣检测结果
        public string? FLJ_SK_Photo { get; set; } // 分零件：锁扣相机存储路径
        public string? JCY_QM_Result { get; set; } // 检测仪：气密检测结果
        public int? JCY_QM_Program { get; set; } // 检测仪：气密检测程序号
        public double? JCY_QM_Pressure { get; set; } // 检测仪：气密检测压力值
        public double? JCY_QM_Leak { get; set; } // 检测仪：气密检测泄漏值
        public string? JCY_NM_Photo1 { get; set; } // 检测仪：内密相机1存储路径
        public string? JCY_NM_Photo2 { get; set; } // 检测仪：内密相机2存储路径
        public string? JCY_NM_Result { get; set; } // 检测仪：内密检测结果
        public string? JCY_KTQM_Result { get; set; } // 检测仪：壳体气密检测结果
        public int? JCY_KTQM_Program { get; set; } // 检测仪：壳体气密程序号
        public double? JCY_KTQM_Pressure { get; set; } // 检测仪：壳体气密压力值
        public double? JCY_KTQM_Leak { get; set; } // 检测仪：壳体气密泄漏值
        public string? FLJ_LH_Model { get; set; } // 分零件：拉坏型号码
        public string? FLJ_LH_Material { get; set; } // 分零件：拉坏物料编码
        public string? FLJ_LH_Result { get; set; } // 分零件：拉坏检测结果
        public string? FLJ_LH_LaserNo { get; set; } // 分零件：拉坏激光打码编码
        public string? FLJ_LH_Photo1 { get; set; } // 分零件：拉坏相机1存储路径
        public string? FLJ_LH_Photo2 { get; set; } // 分零件：拉坏相机2存储路径
        public DateTime? dDate { get; set; } //保存时间

        /// <summary>
        /// 坐标数据的JSON字符串
        /// </summary>
        public string? CoordinateData { get; set; }
    }
}
