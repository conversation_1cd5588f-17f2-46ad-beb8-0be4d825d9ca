using LiveCharts;
using LiveCharts.Wpf;
using Models;
using Newtonsoft.Json;
using System.Windows.Media;
using WorkPieceDataCapture.ViewModels;

namespace ViewModels
{
    public class vm_Chart_Coordinate : vm_Base
    {
        #region 属性
        private SeriesCollection _seriesCollection;
        public SeriesCollection SeriesCollection
        {
            get => _seriesCollection;
            set
            {
                _seriesCollection = value;
                OnPropertyChanged(nameof(SeriesCollection));
            }
        }

        private string _title;
        public string Title
        {
            get => _title;
            set
            {
                _title = value;
                OnPropertyChanged(nameof(Title));
            }
        }

        private string[] _labels;
        public string[] Labels
        {
            get => _labels;
            set
            {
                _labels = value;
                OnPropertyChanged(nameof(Labels));
            }
        }

        private string _serialNo;
        public string SerialNo
        {
            get => _serialNo;
            set
            {
                _serialNo = value;
                OnPropertyChanged(nameof(SerialNo));
            }
        }

        private double _xAxisMinValue;
        public double XAxisMinValue
        {
            get => _xAxisMinValue;
            set
            {
                _xAxisMinValue = value;
                OnPropertyChanged(nameof(XAxisMinValue));
            }
        }
        #endregion

        #region 构造函数
        public vm_Chart_Coordinate()
        {
            Initialize();
        }

        public vm_Chart_Coordinate(tb_WorkPieceDataCapture_Realtime data)
        {
            Initialize();
            LoadCoordinateData(data);
        }
        #endregion

        #region 方法
        private void Initialize()
        {
            SeriesCollection = new SeriesCollection();
            Title = "安全环压力曲线图";
            Labels = new string[0];
            SerialNo = string.Empty;
            XAxisMinValue = 0; // 默认值为0
        }

        /// <summary>
        /// 加载坐标数据
        /// </summary>
        /// <param name="data">包含坐标数据的实体</param>
        public void LoadCoordinateData(tb_WorkPieceDataCapture_Realtime data)
        {
            if (data == null || string.IsNullOrEmpty(data.CoordinateData))
            {
                return;
            }

            try
            {
                // 设置序列号
                SerialNo = data.ZC_SerialNo ?? "未知序列号";

                // 解析JSON字符串为坐标点列表
                var coordinatePoints = JsonConvert.DeserializeObject<List<CoordinatePoint>>(data.CoordinateData);

                if (coordinatePoints == null || coordinatePoints.Count == 0)
                {
                    return;
                }

                // 设置X轴的最小值为第一个坐标点的X值
                if (coordinatePoints.Count > 0)
                {
                    XAxisMinValue = coordinatePoints[0].X;
                }

                // 创建坐标点集合
                var points = new ChartValues<LiveCharts.Defaults.ObservablePoint>();

                // 填充数据 - 直接使用X,Y坐标值
                foreach (var point in coordinatePoints)
                {
                    points.Add(new LiveCharts.Defaults.ObservablePoint(point.X, point.Y));
                }

                // 创建标签 - 使用X坐标值作为标签
                Labels = coordinatePoints.Select(p => p.X.ToString()).ToArray();

                // 创建线形图系列
                SeriesCollection.Clear();
                SeriesCollection.Add(new LineSeries
                {
                    Title = "安全环压力曲线图",
                    Values = points,
                    PointGeometry = DefaultGeometries.Circle,
                    PointGeometrySize = 8,
                    Stroke = new SolidColorBrush(Color.FromRgb(28, 142, 196)),
                    Fill = new SolidColorBrush(Color.FromArgb(50, 28, 142, 196)),
                    LineSmoothness = 0.5 // 添加一些平滑度
                });
            }
            catch (Exception ex)
            {
                // 处理异常
                System.Windows.MessageBox.Show($"加载坐标数据时出错: {ex.Message}", "错误", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }
        #endregion
    }
}
