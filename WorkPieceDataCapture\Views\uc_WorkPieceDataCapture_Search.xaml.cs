﻿using Models;
using System.Windows;
using System.Windows.Controls;
using ViewModels;

namespace WorkPieceDataCapture.Views
{
    /// <summary>
    /// WorkPieceDataCapture_RealTime.xaml 的交互逻辑
    /// </summary>
    public partial class uc_WorkPieceDataCapture_Search : UserControl
    {
        public uc_WorkPieceDataCapture_Search()
        {
            InitializeComponent();
        }
        /// <summary>
        /// 默认的按钮点击事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void Button_Click(object sender, System.Windows.RoutedEventArgs e)
        {
            var btn = sender as Button;
            switch (btn.Content.ToString())
            {
                case "查询":
                    var vm_Search = this.DataContext as vm_WorkPieceDatacapture_Search;
                    if (vm_Search != null && vm_Search.showSearchConditionCommand.CanExecute(null))
                    {
                        vm_Search.showSearchConditionCommand.Execute(null);
                    }
                    break;
                case "导出":
                    var vm_Export = this.DataContext as vm_WorkPieceDatacapture_Search;
                    if (vm_Export != null && vm_Export.showSearchConditionCommand.CanExecute(null))
                    {
                        vm_Export.doExportListCommand.Execute(null);
                    }
                    break;
                case "查看":
                    if (btn.Tag == null || btn.Tag.ToString() == "") return;
                    switch (btn.Tag.ToString())
                    {
                        case "YLQX":
                            var view = new CoordinateChartWindow(this.GridProduceRecord.SelectedItem as tb_WorkPieceDataCapture_Realtime);
                            view.ShowDialog();
                            break;
                        default:
                            var result = GlobalClass.openFile(btn.Tag.ToString());
                            if (!result.bSuccess)
                            {
                                MessageBox.Show(result.strMsg);
                            }
                            break;
                    }
                    break;
                default:
                    break;
            }
        }
        /// <summary>
        /// 导出一行明细的按钮事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void Export_Click(object sender, System.Windows.RoutedEventArgs e)
        {
            if (this.GridProduceRecord.SelectedIndex > -1)
            {
                var vm = this.DataContext as vm_WorkPieceDatacapture_Search;
                if (vm != null && vm.doExportDetailCommand.CanExecute(null))
                {
                    vm.doExportDetailCommand.Execute(this.GridProduceRecord.SelectedItem);
                }
            }
        }
        //双击查看明细
        private void GridProduceRecord_PreviewMouseDoubleClick(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            var datagrid = sender as DataGrid;
            if (datagrid.SelectedIndex < 0)
            {
                return;
            }
            var vm = this.DataContext as vm_WorkPieceDatacapture_Search;
            if (vm != null && vm.RowDoubleClickCommand.CanExecute(null))
            {
                vm.RowDoubleClickCommand.Execute(this.GridProduceRecord.SelectedItem);
            }
        }

        private void GridProduceRecord_PreviewMouseDown(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {

        }
    }
}
