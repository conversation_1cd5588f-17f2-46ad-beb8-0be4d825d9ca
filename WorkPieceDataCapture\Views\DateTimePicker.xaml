﻿<Window x:Class="UserControls.CustControls.DateTimePicker" WindowStyle="None" ResizeMode="NoResize" WindowStartupLocation="CenterOwner"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d" Height="270" Width="250"  Loaded="Window_Loaded">
    <Grid Background="#015478" >
        <Grid.RowDefinitions>
            <RowDefinition></RowDefinition>
            <RowDefinition Height="30"></RowDefinition>
        </Grid.RowDefinitions>
        <Viewbox Stretch="Fill" >
                <Calendar Name="cal" HorizontalAlignment="Left" VerticalAlignment="Top" />
        </Viewbox>
        <WrapPanel Grid.Row="1" HorizontalAlignment="Center" Margin="0,0,0,5">
            <Label Content="时" Grid.Row="1" Foreground="White" FontWeight="Bold"></Label>
            <ComboBox Name="cbHour" Width="50" FontSize="16" HorizontalContentAlignment="Center" VerticalContentAlignment="Center" >
                <ComboBoxItem>00</ComboBoxItem>
                <ComboBoxItem>01</ComboBoxItem>
                <ComboBoxItem>02</ComboBoxItem>
                <ComboBoxItem>03</ComboBoxItem>
                <ComboBoxItem>04</ComboBoxItem>
                <ComboBoxItem>05</ComboBoxItem>
                <ComboBoxItem>06</ComboBoxItem>
                <ComboBoxItem>07</ComboBoxItem>
                <ComboBoxItem>08</ComboBoxItem>
                <ComboBoxItem>09</ComboBoxItem>
                <ComboBoxItem>10</ComboBoxItem>
                <ComboBoxItem>11</ComboBoxItem>
                <ComboBoxItem>12</ComboBoxItem>
                <ComboBoxItem>13</ComboBoxItem>
                <ComboBoxItem>14</ComboBoxItem>
                <ComboBoxItem>15</ComboBoxItem>
                <ComboBoxItem>16</ComboBoxItem>
                <ComboBoxItem>17</ComboBoxItem>
                <ComboBoxItem>18</ComboBoxItem>
                <ComboBoxItem>19</ComboBoxItem>
                <ComboBoxItem>20</ComboBoxItem>
                <ComboBoxItem>21</ComboBoxItem>
                <ComboBoxItem>22</ComboBoxItem>
                <ComboBoxItem>23</ComboBoxItem>
            </ComboBox>
            <Label Content="分" Grid.Row="1" Foreground="White" FontWeight="Bold"></Label>
            <ComboBox Name="cbMinute" Width="50" FontSize="16" HorizontalContentAlignment="Center" VerticalContentAlignment="Center" >
                <ComboBoxItem>00</ComboBoxItem>
                <ComboBoxItem>01</ComboBoxItem>
                <ComboBoxItem>02</ComboBoxItem>
                <ComboBoxItem>03</ComboBoxItem>
                <ComboBoxItem>04</ComboBoxItem>
                <ComboBoxItem>05</ComboBoxItem>
                <ComboBoxItem>06</ComboBoxItem>
                <ComboBoxItem>07</ComboBoxItem>
                <ComboBoxItem>08</ComboBoxItem>
                <ComboBoxItem>09</ComboBoxItem>
                <ComboBoxItem>10</ComboBoxItem>
                <ComboBoxItem>11</ComboBoxItem>
                <ComboBoxItem>12</ComboBoxItem>
                <ComboBoxItem>13</ComboBoxItem>
                <ComboBoxItem>14</ComboBoxItem>
                <ComboBoxItem>15</ComboBoxItem>
                <ComboBoxItem>16</ComboBoxItem>
                <ComboBoxItem>17</ComboBoxItem>
                <ComboBoxItem>18</ComboBoxItem>
                <ComboBoxItem>19</ComboBoxItem>
                <ComboBoxItem>20</ComboBoxItem>
                <ComboBoxItem>21</ComboBoxItem>
                <ComboBoxItem>22</ComboBoxItem>
                <ComboBoxItem>23</ComboBoxItem>
                <ComboBoxItem>24</ComboBoxItem>
                <ComboBoxItem>25</ComboBoxItem>
                <ComboBoxItem>26</ComboBoxItem>
                <ComboBoxItem>27</ComboBoxItem>
                <ComboBoxItem>28</ComboBoxItem>
                <ComboBoxItem>29</ComboBoxItem>
                <ComboBoxItem>30</ComboBoxItem>
                <ComboBoxItem>31</ComboBoxItem>
                <ComboBoxItem>32</ComboBoxItem>
                <ComboBoxItem>33</ComboBoxItem>
                <ComboBoxItem>34</ComboBoxItem>
                <ComboBoxItem>35</ComboBoxItem>
                <ComboBoxItem>36</ComboBoxItem>
                <ComboBoxItem>37</ComboBoxItem>
                <ComboBoxItem>38</ComboBoxItem>
                <ComboBoxItem>39</ComboBoxItem>
                <ComboBoxItem>40</ComboBoxItem>
                <ComboBoxItem>41</ComboBoxItem>
                <ComboBoxItem>42</ComboBoxItem>
                <ComboBoxItem>43</ComboBoxItem>
                <ComboBoxItem>44</ComboBoxItem>
                <ComboBoxItem>45</ComboBoxItem>
                <ComboBoxItem>46</ComboBoxItem>
                <ComboBoxItem>47</ComboBoxItem>
                <ComboBoxItem>48</ComboBoxItem>
                <ComboBoxItem>49</ComboBoxItem>
                <ComboBoxItem>50</ComboBoxItem>
                <ComboBoxItem>51</ComboBoxItem>
                <ComboBoxItem>52</ComboBoxItem>
                <ComboBoxItem>53</ComboBoxItem>
                <ComboBoxItem>54</ComboBoxItem>
                <ComboBoxItem>55</ComboBoxItem>
                <ComboBoxItem>56</ComboBoxItem>
                <ComboBoxItem>57</ComboBoxItem>
                <ComboBoxItem>58</ComboBoxItem>
                <ComboBoxItem>59</ComboBoxItem>
            </ComboBox>
            <Button Name="btnConfirm" Width="40" Background="Transparent" Click="btnConfirm_Click">
                <Image Source="pack://siteoforigin:,,,/Resources/good.png" />
            </Button>
            <Button Name="btnCancel" Width="40" Background="Transparent" Click="btnCancel_Click">
                <Image Source="pack://siteoforigin:,,,/Resources/stop.png" />
            </Button>
        </WrapPanel>
    </Grid>
</Window>
