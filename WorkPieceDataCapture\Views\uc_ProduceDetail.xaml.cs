﻿using Models;
using System.Windows;
using System.Windows.Controls;
using ViewModels;

namespace WorkPieceDataCapture.Views
{
    /// <summary>
    /// uc_ProduceDetail.xaml 的交互逻辑
    /// </summary>
    public partial class uc_ProduceDetail : UserControl
    {
        tb_WorkPieceDataCapture_Realtime datamodel;
        public uc_ProduceDetail(tb_WorkPieceDataCapture_Realtime model)
        {
            InitializeComponent();
            datamodel = model;
            this.DataContext = new vm_ProduceDetail(model);
        }

        private void Button_Click(object sender, System.Windows.RoutedEventArgs e)
        {
            var btn = sender as Button;
            if (btn.Content == null || btn.Content.ToString() == "") return;
            switch (btn.Tag.ToString())
            {
                case "path":
                    var result = GlobalClass.openFile(btn.Content.ToString());
                    if (!result.bSuccess)
                    {
                        MessageBox.Show(result.strMsg);
                    }
                    break;
                case "ylqxt":
                    // 打开图表窗口
                    var chartWindow = new CoordinateChartWindow(datamodel);
                    chartWindow.ShowDialog();
                    break;
                default:
                    break;
            }

        }
    }
}
