<?xml version="1.0" encoding="utf-8" ?>
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:schemaLocation="http://www.nlog-project.org/schemas/NLog.xsd NLog.xsd"
      autoReload="true"
      throwExceptions="false"
      internalLogLevel="Off" internalLogFile="c:\temp\nlog-internal.log">
	<variable name="appName" value="ConsoleAppDemo"/>
	<targets>
		<target name="logconsole" xsi:type="Console"
				layout="${longdate} [${uppercase:${level}}] ${message} ${exception:format=ToString}" 
		/>
		<target name="logfile" 
				xsi:type="File" 
				fileName="${basedir}/logs/${shortdate}.log"
				layout="${longdate} [${uppercase:${level}}] ${message} ${exception:format=ToString}"
				maxArchiveFiles="999"
				archiveFileName="${basedir}/logs/${shortdate}.log"
				createDirs="true"
				archiveAboveSize="20102400"
				archiveEvery="Day"
				encoding="UTF-8"
		/>
	</targets>
	<rules>
		<logger name="*" minlevel="Debug" writeTo="logfile" />
	</rules>
</nlog>