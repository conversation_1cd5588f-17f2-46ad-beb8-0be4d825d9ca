﻿﻿using Interfaces.Model;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Models
{
    /// <summary>
    /// 工件加工数据采集请求实体类
    /// </summary>
    public class WorkPieceDataCapture_Request : IDataModel
    {
        //主键
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int? iID { get; set; } // 自增ID
        [Key]
        public string? ZC_SerialNo { get; set; } // 总成设备标识码
        public string? ZC_Model { get; set; } // 总成型号
        public string? ZC_Time { get; set; } // 总成生产时间
        public string? ZC_LaserNo { get; set; } // 总成激光打码编码
        public string? ZC_Result { get; set; } // 总成综合结果
        public string? FLJ_ZT_Model { get; set; } // 分零件：主体型号码
        public string? FLJ_ZT_Material { get; set; } // 分零件：主体物料编码
        public string? FLJ_ZT_Result { get; set; } // 分零件：主体异物位移检测结果
        public double? FLJ_ZT_Value { get; set; } // 分零件：主体异物位移检测值
        public double? FLJ_ZT_Upper { get; set; } // 分零件：主体异物位移检测参数上限
        public double? FLJ_ZT_Lower { get; set; } // 分零件：主体异物位移检测参数下限
        public string? FLJ_YCQ_Model { get; set; } // 分零件：一次圈型号码
        public string? FLJ_YCQ_Material { get; set; } // 分零件：一次圈物料编码
        public string? FLJ_YCQ_Result { get; set; } // 分零件：一次圈检测结果
        public double? FLJ_YCQ_Value { get; set; } // 分零件：一次圈位移检测值
        public double? FLJ_YCQ_Upper { get; set; } // 分零件：一次圈位移检测参数上限
        public double? FLJ_YCQ_Lower { get; set; } // 分零件：一次圈位移检测参数下限
        public string? FLJ_GLH_Model { get; set; } // 分零件：隔离环型号码
        public string? FLJ_GLH_Material { get; set; } // 分零件：隔离环物料编码
        public string? FLJ_GLH_Result { get; set; } // 分零件：隔离环检测结果
        public double? FLJ_GLH_Value { get; set; } // 分零件：隔离环检测值
        public double? FLJ_GLH_Upper { get; set; } // 分零件：隔离环位移检测参数上限
        public double? FLJ_GLH_Lower { get; set; } // 分零件：隔离环位移检测参数下限
        public string? FLJ_ECQ_Model { get; set; } // 分零件：二次圈型号码
        public string? FLJ_ECQ_Material { get; set; } // 分零件：二次圈物料编码
        public string? FLJ_ECQ_Result { get; set; } // 分零件：二次圈检测结果
        public double? FLJ_ECQ_Value { get; set; } // 分零件：二次圈位移检测值
        public double? FLJ_ECQ_Upper { get; set; } // 分零件：二次圈位移检测参数上限
        public double? FLJ_ECQ_Lower { get; set; } // 分零件：二次圈位移检测参数下限
        public string? FLJ_KZH_Model { get; set; } // 分零件：安全环型号码
        public string? FLJ_KZH_Material { get; set; } // 分零件：安全环物料编码
        public string? FLJ_KZH_Result { get; set; } // 分零件：安全环检测结果
        public double? FLJ_KZH_Value { get; set; } // 分零件：安全环位移检测值
        public double? FLJ_KZH_Upper { get; set; } // 分零件：安全环位移检测参数上限
        public double? FLJ_KZH_Lower { get; set; } // 分零件：安全环位移检测参数下限
        public string? FLJ_KZH_PressureResult { get; set; } // 分零件：安全环压力检测结果
        public string? FLJ_WM_Model { get; set; } // 分零件：外密型号码
        public string? FLJ_WM_Material { get; set; } // 分零件：外密物料编码
        public string? FLJ_WM_Result { get; set; } // 分零件：外密检测结果
        public string? FLJ_WM_Photo1 { get; set; } // 分零件：外密相机1存储路径
        public string? FLJ_WM_Photo2 { get; set; } // 分零件：外密相机2存储路径
        public string? FLJ_SK_Model { get; set; } // 分零件：锁扣型号码
        public string? FLJ_SK_Material { get; set; } // 分零件：锁扣物料编码
        public string? FLJ_SK_Result { get; set; } // 分零件：锁扣检测结果
        public string? FLJ_SK_Photo { get; set; } // 分零件：锁扣相机存储路径
        public string? JCY_QM_Result { get; set; } // 检测仪：气密检测结果
        public int? JCY_QM_Program { get; set; } // 检测仪：气密检测程序号
        public double? JCY_QM_Pressure { get; set; } // 检测仪：气密检测压力值
        public double? JCY_QM_Leak { get; set; } // 检测仪：气密检测泄漏值
        public string? JCY_NM_Photo1 { get; set; } // 检测仪：内密相机1存储路径
        public string? JCY_NM_Photo2 { get; set; } // 检测仪：内密相机2存储路径
        public string? JCY_NM_Result { get; set; } // 检测仪：内密检测结果
        public string? JCY_KTQM_Result { get; set; } // 检测仪：壳体气密检测结果
        public int? JCY_KTQM_Program { get; set; } // 检测仪：壳体气密程序号
        public double? JCY_KTQM_Pressure { get; set; } // 检测仪：壳体气密压力值
        public double? JCY_KTQM_Leak { get; set; } // 检测仪：壳体气密泄漏值
        public string? FLJ_LH_Model { get; set; } // 分零件：拉坏型号码
        public string? FLJ_LH_Material { get; set; } // 分零件：拉坏物料编码
        public string? FLJ_LH_Result { get; set; } // 分零件：拉坏检测结果
        public string? FLJ_LH_LaserNo { get; set; } // 分零件：拉坏激光打码编码
        public string? FLJ_LH_Photo1 { get; set; } // 分零件：拉坏相机1存储路径
        public string? FLJ_LH_Photo2 { get; set; } // 分零件：拉坏相机2存储路径
        public DateTime? dDate { get; set; } //保存时间
        public bool? CheckLaserNo { get; set; } //是否校验激光码
        public double? YLQX_X1 { get; set; }
        public double? YLQX_Y1 { get; set; }
        public double? YLQX_X2 { get; set; }
        public double? YLQX_Y2 { get; set; }
        public double? YLQX_X3 { get; set; }
        public double? YLQX_Y3 { get; set; }
        public double? YLQX_X4 { get; set; }
        public double? YLQX_Y4 { get; set; }
        public double? YLQX_X5 { get; set; }
        public double? YLQX_Y5 { get; set; }
        public double? YLQX_X6 { get; set; }
        public double? YLQX_Y6 { get; set; }
        public double? YLQX_X7 { get; set; }
        public double? YLQX_Y7 { get; set; }
        public double? YLQX_X8 { get; set; }
        public double? YLQX_Y8 { get; set; }
        public double? YLQX_X9 { get; set; }
        public double? YLQX_Y9 { get; set; }
        public double? YLQX_X10 { get; set; }
        public double? YLQX_Y10 { get; set; }
        public double? YLQX_X11 { get; set; }
        public double? YLQX_Y11 { get; set; }
        public double? YLQX_X12 { get; set; }
        public double? YLQX_Y12 { get; set; }
        public double? YLQX_X13 { get; set; }
        public double? YLQX_Y13 { get; set; }
        public double? YLQX_X14 { get; set; }
        public double? YLQX_Y14 { get; set; }
        public double? YLQX_X15 { get; set; }
        public double? YLQX_Y15 { get; set; }
        public double? YLQX_X16 { get; set; }
        public double? YLQX_Y16 { get; set; }
        public double? YLQX_X17 { get; set; }
        public double? YLQX_Y17 { get; set; }
        public double? YLQX_X18 { get; set; }
        public double? YLQX_Y18 { get; set; }
        public double? YLQX_X19 { get; set; }
        public double? YLQX_Y19 { get; set; }
        public double? YLQX_X20 { get; set; }
        public double? YLQX_Y20 { get; set; }
        public double? YLQX_X21 { get; set; }
        public double? YLQX_Y21 { get; set; }
        public double? YLQX_X22 { get; set; }
        public double? YLQX_Y22 { get; set; }
        public double? YLQX_X23 { get; set; }
        public double? YLQX_Y23 { get; set; }
        public double? YLQX_X24 { get; set; }
        public double? YLQX_Y24 { get; set; }
        public double? YLQX_X25 { get; set; }
        public double? YLQX_Y25 { get; set; }
        public double? YLQX_X26 { get; set; }
        public double? YLQX_Y26 { get; set; }
        public double? YLQX_X27 { get; set; }
        public double? YLQX_Y27 { get; set; }
        public double? YLQX_X28 { get; set; }
        public double? YLQX_Y28 { get; set; }
        public double? YLQX_X29 { get; set; }
        public double? YLQX_Y29 { get; set; }
        public double? YLQX_X30 { get; set; }
        public double? YLQX_Y30 { get; set; }
        public double? YLQX_X31 { get; set; }
        public double? YLQX_Y31 { get; set; }
        public double? YLQX_X32 { get; set; }
        public double? YLQX_Y32 { get; set; }
        public double? YLQX_X33 { get; set; }
        public double? YLQX_Y33 { get; set; }
        public double? YLQX_X34 { get; set; }
        public double? YLQX_Y34 { get; set; }
        public double? YLQX_X35 { get; set; }
        public double? YLQX_Y35 { get; set; }
        public double? YLQX_X36 { get; set; }
        public double? YLQX_Y36 { get; set; }
        public double? YLQX_X37 { get; set; }
        public double? YLQX_Y37 { get; set; }
        public double? YLQX_X38 { get; set; }
        public double? YLQX_Y38 { get; set; }
        public double? YLQX_X39 { get; set; }
        public double? YLQX_Y39 { get; set; }
        public double? YLQX_X40 { get; set; }
        public double? YLQX_Y40 { get; set; }
        public double? YLQX_X41 { get; set; }
        public double? YLQX_Y41 { get; set; }
        public double? YLQX_X42 { get; set; }
        public double? YLQX_Y42 { get; set; }
        public double? YLQX_X43 { get; set; }
        public double? YLQX_Y43 { get; set; }
        public double? YLQX_X44 { get; set; }
        public double? YLQX_Y44 { get; set; }
        public double? YLQX_X45 { get; set; }
        public double? YLQX_Y45 { get; set; }
        public double? YLQX_X46 { get; set; }
        public double? YLQX_Y46 { get; set; }
        public double? YLQX_X47 { get; set; }
        public double? YLQX_Y47 { get; set; }
        public double? YLQX_X48 { get; set; }
        public double? YLQX_Y48 { get; set; }
        public double? YLQX_X49 { get; set; }
        public double? YLQX_Y49 { get; set; }
        public double? YLQX_X50 { get; set; }
        public double? YLQX_Y50 { get; set; }
        public double? YLQX_X51 { get; set; }
        public double? YLQX_Y51 { get; set; }
        public double? YLQX_X52 { get; set; }
        public double? YLQX_Y52 { get; set; }
        public double? YLQX_X53 { get; set; }
        public double? YLQX_Y53 { get; set; }
        public double? YLQX_X54 { get; set; }
        public double? YLQX_Y54 { get; set; }
        public double? YLQX_X55 { get; set; }
        public double? YLQX_Y55 { get; set; }
        public double? YLQX_X56 { get; set; }
        public double? YLQX_Y56 { get; set; }
        public double? YLQX_X57 { get; set; }
        public double? YLQX_Y57 { get; set; }
        public double? YLQX_X58 { get; set; }
        public double? YLQX_Y58 { get; set; }
        public double? YLQX_X59 { get; set; }
        public double? YLQX_Y59 { get; set; }
        public double? YLQX_X60 { get; set; }
        public double? YLQX_Y60 { get; set; }
        public double? YLQX_X61 { get; set; }
        public double? YLQX_Y61 { get; set; }
        public double? YLQX_X62 { get; set; }
        public double? YLQX_Y62 { get; set; }
        public double? YLQX_X63 { get; set; }
        public double? YLQX_Y63 { get; set; }
        public double? YLQX_X64 { get; set; }
        public double? YLQX_Y64 { get; set; }
        public double? YLQX_X65 { get; set; }
        public double? YLQX_Y65 { get; set; }
        public double? YLQX_X66 { get; set; }
        public double? YLQX_Y66 { get; set; }
        public double? YLQX_X67 { get; set; }
        public double? YLQX_Y67 { get; set; }
        public double? YLQX_X68 { get; set; }
        public double? YLQX_Y68 { get; set; }
        public double? YLQX_X69 { get; set; }
        public double? YLQX_Y69 { get; set; }
        public double? YLQX_X70 { get; set; }
        public double? YLQX_Y70 { get; set; }
        public double? YLQX_X71 { get; set; }
        public double? YLQX_Y71 { get; set; }
        public double? YLQX_X72 { get; set; }
        public double? YLQX_Y72 { get; set; }
        public double? YLQX_X73 { get; set; }
        public double? YLQX_Y73 { get; set; }
        public double? YLQX_X74 { get; set; }
        public double? YLQX_Y74 { get; set; }
        public double? YLQX_X75 { get; set; }
        public double? YLQX_Y75 { get; set; }
        public double? YLQX_X76 { get; set; }
        public double? YLQX_Y76 { get; set; }
        public double? YLQX_X77 { get; set; }
        public double? YLQX_Y77 { get; set; }
        public double? YLQX_X78 { get; set; }
        public double? YLQX_Y78 { get; set; }
        public double? YLQX_X79 { get; set; }
        public double? YLQX_Y79 { get; set; }
        public double? YLQX_X80 { get; set; }
        public double? YLQX_Y80 { get; set; }
        public double? YLQX_X81 { get; set; }
        public double? YLQX_Y81 { get; set; }
        public double? YLQX_X82 { get; set; }
        public double? YLQX_Y82 { get; set; }
        public double? YLQX_X83 { get; set; }
        public double? YLQX_Y83 { get; set; }
        public double? YLQX_X84 { get; set; }
        public double? YLQX_Y84 { get; set; }
        public double? YLQX_X85 { get; set; }
        public double? YLQX_Y85 { get; set; }
        public double? YLQX_X86 { get; set; }
        public double? YLQX_Y86 { get; set; }
        public double? YLQX_X87 { get; set; }
        public double? YLQX_Y87 { get; set; }
        public double? YLQX_X88 { get; set; }
        public double? YLQX_Y88 { get; set; }
        public double? YLQX_X89 { get; set; }
        public double? YLQX_Y89 { get; set; }
        public double? YLQX_X90 { get; set; }
        public double? YLQX_Y90 { get; set; }
        public double? YLQX_X91 { get; set; }
        public double? YLQX_Y91 { get; set; }
        public double? YLQX_X92 { get; set; }
        public double? YLQX_Y92 { get; set; }
        public double? YLQX_X93 { get; set; }
        public double? YLQX_Y93 { get; set; }
        public double? YLQX_X94 { get; set; }
        public double? YLQX_Y94 { get; set; }
        public double? YLQX_X95 { get; set; }
        public double? YLQX_Y95 { get; set; }
        public double? YLQX_X96 { get; set; }
        public double? YLQX_Y96 { get; set; }
        public double? YLQX_X97 { get; set; }
        public double? YLQX_Y97 { get; set; }
        public double? YLQX_X98 { get; set; }
        public double? YLQX_Y98 { get; set; }
        public double? YLQX_X99 { get; set; }
        public double? YLQX_Y99 { get; set; }
        public double? YLQX_X100 { get; set; }
        public double? YLQX_Y100 { get; set; }
    }

    /// <summary>
    /// 坐标点模型
    /// </summary>
    public class CoordinatePoint
    {
        public double X { get; set; }
        public double Y { get; set; }
    }
}

