﻿<Window x:Class="WorkPieceDataCapture.Views.SearchCondition" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" WindowStartupLocation="CenterScreen"
 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" mc:Ignorable="d"  Title="查询条件" Height="460" Width="1130" Loaded="Window_Loaded">
    <Grid>
        <WrapPanel Background="#015478">
            <Label Content="总成" Style="{DynamicResource DataDetailTitle}" Height="60"></Label>
            <Border Style="{DynamicResource DataDetailContentBorder}" Width="950" Height="60">
                <Grid Height="60" Background="Transparent">
                    <Grid.RowDefinitions>
                        <RowDefinition></RowDefinition>
                        <RowDefinition></RowDefinition>
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"></ColumnDefinition>
                        <ColumnDefinition Width="203"></ColumnDefinition>
                    </Grid.ColumnDefinitions>
                    <WrapPanel Grid.Row="0">
                        <Label Content="标识码：" Style="{DynamicResource DataDetailFieldTitle_4Words}" ></Label>
                        <TextBox Text="{Binding ZC_SerialNo}" Style="{DynamicResource TextBox_Normal}" ></TextBox>
                        <Label Content="生产日期：" Style="{DynamicResource DataDetailFieldTitle_4Words}"></Label>
                        <TextBox Name="txtDate_Begin" Text="{Binding dDate_Begin,StringFormat='yyyy-MM-dd HH:mm'}" Style="{DynamicResource TextBox_Normal}" IsReadOnly="True" PreviewMouseDown="event_DateTimePicker"></TextBox>
                        <Label Content="--" Style="{DynamicResource DataDetailFieldTitle_4Words}" Width="22" ></Label>
                        <TextBox Name="txtDate_End" Text="{Binding dDate_End,StringFormat='yyyy-MM-dd HH:mm'}" Style="{DynamicResource TextBox_Normal}" IsReadOnly="True" PreviewMouseDown="event_DateTimePicker"></TextBox>
                    </WrapPanel>
                    <WrapPanel Grid.Row="1">
                        <Label Content="总成型号：" Style="{DynamicResource DataDetailFieldTitle_4Words}"></Label>
                        <TextBox Text="{Binding ZC_Model}" Style="{DynamicResource TextBox_Normal}"></TextBox>
                        <Label Content="激光打标码：" Style="{DynamicResource DataDetailFieldTitle_4Words}"></Label>
                        <TextBox Text="{Binding ZC_LaserNo}" Style="{DynamicResource TextBox_Normal}" Width="322"></TextBox>
                    </WrapPanel>
                    <WrapPanel Grid.Column="1" Grid.RowSpan="2" VerticalAlignment="Center">
                        <Label Content="综合结果：" Style="{DynamicResource DataDetailFieldTitle_4Words}" FontWeight="Bold" Grid.Column="1" Grid.RowSpan="2" HorizontalAlignment="Left"/>
                        <ComboBox Grid.Column="1" Grid.RowSpan="2" Width="80" FontSize="20" Text="{Binding ZC_Result}" >
                            <ComboBoxItem  IsSelected="True">全部</ComboBoxItem>
                            <ComboBoxItem >OK</ComboBoxItem>
                            <ComboBoxItem >NG</ComboBoxItem>
                        </ComboBox>
                    </WrapPanel>
                </Grid>
            </Border>
            <Label Content="分零件：主体" Style="{DynamicResource DataDetailTitle}" Height="40"></Label>
            <Border Style="{DynamicResource DataDetailContentBorder}" Width="950" Height="40">
                <WrapPanel Grid.Row="0" Grid.ColumnSpan="2" VerticalAlignment="Center">
                    <Label Content="型号码：" Style="{DynamicResource DataDetailFieldTitle_4Words}" ></Label>
                    <TextBox Text="{Binding FLJ_ZT_Model}" Style="{DynamicResource TextBox_Normal}" ></TextBox>
                    <Label Content="物料编码：" Style="{DynamicResource DataDetailFieldTitle_4Words}"></Label>
                    <TextBox Text="{Binding FLJ_ZT_Material}" Style="{DynamicResource TextBox_Normal}" ></TextBox>
                    <Label Content="检测值：" Style="{DynamicResource DataDetailFieldTitle_4Words}"></Label>
                    <TextBox Text="{Binding FLJ_ZT_Begin}" Style="{DynamicResource TextBox_Short}" ></TextBox>
                    <Label Content="--" Style="{DynamicResource DataDetailFieldTitle_4Words}" Width="25"></Label>
                    <TextBox Text="{Binding FLJ_ZT_End}" Style="{DynamicResource TextBox_Short}" ></TextBox>
                    <Label Style="{DynamicResource DataDetailFieldTitle_4Words}" FontWeight="Bold" Content="检测结果："></Label>
                    <ComboBox Width="80" FontSize="20" Text="{Binding FLJ_ZT_Result}">
                        <ComboBoxItem  IsSelected="True" Content="全部"/>
                        <ComboBoxItem Content="OK" />
                        <ComboBoxItem Content="NG" />
                    </ComboBox>
                </WrapPanel>
            </Border>
            <Label Content="分零件：一次圈" Style="{DynamicResource DataDetailTitle}" Height="40"></Label>
            <Border Style="{DynamicResource DataDetailContentBorder}" Width="950" Height="40">
                <WrapPanel Grid.Row="0" Grid.ColumnSpan="2" VerticalAlignment="Center">
                    <Label Content="型号码：" Style="{DynamicResource DataDetailFieldTitle_4Words}" ></Label>
                    <TextBox Text="{Binding FLJ_YCQ_Model}" Style="{DynamicResource TextBox_Normal}" ></TextBox>
                    <Label Content="物料编码：" Style="{DynamicResource DataDetailFieldTitle_4Words}"></Label>
                    <TextBox Text="{Binding FLJ_YCQ_Material}" Style="{DynamicResource TextBox_Normal}" ></TextBox>
                </WrapPanel>
            </Border>
            <Label Content="分零件：隔离环" Style="{DynamicResource DataDetailTitle}" Height="40"></Label>
            <Border Style="{DynamicResource DataDetailContentBorder}" Width="950" Height="40">
                <WrapPanel Grid.Row="0" Grid.ColumnSpan="2" VerticalAlignment="Center">
                    <Label Content="型号码：" Style="{DynamicResource DataDetailFieldTitle_4Words}" ></Label>
                    <TextBox Text="{Binding FLJ_GLH_Model}" Style="{DynamicResource TextBox_Normal}" ></TextBox>
                    <Label Content="物料编码：" Style="{DynamicResource DataDetailFieldTitle_4Words}"></Label>
                    <TextBox Text="{Binding FLJ_GLH_Material}" Style="{DynamicResource TextBox_Normal}" ></TextBox>
                    <Label Content="检测值：" Style="{DynamicResource DataDetailFieldTitle_4Words}"></Label>
                    <TextBox Text="{Binding FLJ_GLH_Begin}" Style="{DynamicResource TextBox_Short}" ></TextBox>
                    <Label Content="--" Style="{DynamicResource DataDetailFieldTitle_4Words}" Width="25"></Label>
                    <TextBox Text="{Binding FLJ_GLH_End}" Style="{DynamicResource TextBox_Short}" ></TextBox>
                    <Label Style="{DynamicResource DataDetailFieldTitle_4Words}" FontWeight="Bold" Content="检测结果："></Label>
                    <ComboBox Width="80" FontSize="20" Text="{Binding FLJ_GLH_Result}" >
                        <ComboBoxItem  IsSelected="True" Content="全部"/>
                        <ComboBoxItem Content="OK" />
                        <ComboBoxItem Content="NG" />
                    </ComboBox>
                </WrapPanel>
            </Border>
            <Label Content="分零件：二次圈" Style="{DynamicResource DataDetailTitle}" Height="40"></Label>
            <Border Style="{DynamicResource DataDetailContentBorder}" Width="950" Height="40">
                <WrapPanel Grid.Row="0" Grid.ColumnSpan="2" VerticalAlignment="Center">
                    <Label Content="型号码：" Style="{DynamicResource DataDetailFieldTitle_4Words}" ></Label>
                    <TextBox Text="{Binding FLJ_ECQ_Model}" Style="{DynamicResource TextBox_Normal}" ></TextBox>
                    <Label Content="物料编码：" Style="{DynamicResource DataDetailFieldTitle_4Words}"></Label>
                    <TextBox Text="{Binding FLJ_ECQ_Material}" Style="{DynamicResource TextBox_Normal}" ></TextBox>
                    <Label Content="检测值：" Style="{DynamicResource DataDetailFieldTitle_4Words}"></Label>
                    <TextBox Text="{Binding FLJ_ECQ_Begin}" Style="{DynamicResource TextBox_Short}" ></TextBox>
                    <Label Content="--" Style="{DynamicResource DataDetailFieldTitle_4Words}" Width="25"></Label>
                    <TextBox Text="{Binding FLJ_ECQ_End}" Style="{DynamicResource TextBox_Short}" ></TextBox>
                    <Label Style="{DynamicResource DataDetailFieldTitle_4Words}" FontWeight="Bold" Content="检测结果："></Label>
                    <ComboBox Width="80" FontSize="20" Text="{Binding FLJ_ECQ_Result}">
                        <ComboBoxItem  IsSelected="True" Content="全部"/>
                        <ComboBoxItem Content="OK" />
                        <ComboBoxItem Content="NG" />
                    </ComboBox>
                </WrapPanel>
            </Border>
            <Label Content="分零件：安全环" Style="{DynamicResource DataDetailTitle}" Height="40"></Label>
            <Border Style="{DynamicResource DataDetailContentBorder}" Width="950" Height="40">
                <WrapPanel Grid.Row="0" Grid.ColumnSpan="2" VerticalAlignment="Center">
                    <Label Content="型号码：" Style="{DynamicResource DataDetailFieldTitle_4Words}" ></Label>
                    <TextBox Text="{Binding FLJ_KZH_Model}" Style="{DynamicResource TextBox_Normal}" ></TextBox>
                    <Label Content="物料编码：" Style="{DynamicResource DataDetailFieldTitle_4Words}"></Label>
                    <TextBox Text="{Binding FLJ_KZH_Material}" Style="{DynamicResource TextBox_Normal}" ></TextBox>
                    <Label Content="检测值：" Style="{DynamicResource DataDetailFieldTitle_4Words}"></Label>
                    <TextBox Text="{Binding FLJ_KZH_Begin}" Style="{DynamicResource TextBox_Short}" ></TextBox>
                    <Label Content="--" Style="{DynamicResource DataDetailFieldTitle_4Words}" Width="25"></Label>
                    <TextBox Text="{Binding FLJ_KZH_End}" Style="{DynamicResource TextBox_Short}" ></TextBox>
                    <Label Style="{DynamicResource DataDetailFieldTitle_4Words}" FontWeight="Bold" Content="检测结果："></Label>
                    <ComboBox Width="80" FontSize="20"  Text="{Binding FLJ_KZH_Result}">
                        <ComboBoxItem  IsSelected="True" Content="全部"/>
                        <ComboBoxItem Content="OK" />
                        <ComboBoxItem Content="NG" />
                    </ComboBox>
                </WrapPanel>
            </Border>
            <Label Content="分零件：锁扣" Style="{DynamicResource DataDetailTitle}" Height="40"></Label>
            <Border Style="{DynamicResource DataDetailContentBorder}" Width="950" Height="40">
                <WrapPanel Grid.Row="0" Grid.ColumnSpan="2" VerticalAlignment="Center">
                    <Label Content="型号码：" Style="{DynamicResource DataDetailFieldTitle_4Words}" ></Label>
                    <TextBox Text="{Binding FLJ_SK_Model}" Style="{DynamicResource TextBox_Normal}" ></TextBox>
                    <Label Content="物料编码：" Style="{DynamicResource DataDetailFieldTitle_4Words}"></Label>
                    <TextBox Text="{Binding FLJ_SK_Material}" Style="{DynamicResource TextBox_Normal}" ></TextBox>
                    <Label Style="{DynamicResource DataDetailFieldTitle_4Words}" FontWeight="Bold" Content="检测结果：" Margin="245,0,0,0"></Label>
                    <ComboBox Width="80" FontSize="20" Text="{Binding FLJ_SK_Result}" >
                        <ComboBoxItem  IsSelected="True" Content="全部"/>
                        <ComboBoxItem Content="OK" />
                        <ComboBoxItem Content="NG" />
                    </ComboBox>
                </WrapPanel>
            </Border>
            <Label Content="检测仪：内密气密" Style="{DynamicResource DataDetailTitle}" Height="40"></Label>
            <Border  Style="{DynamicResource DataDetailContentBorder}" Width="950" Height="40">
                <WrapPanel Grid.Row="0" Grid.ColumnSpan="2" VerticalAlignment="Center">
                    <Label Content="程序号：" Style="{DynamicResource DataDetailFieldTitle_4Words}"></Label>
                    <TextBox Text="{Binding JCY_QM_Program}" Style="{DynamicResource TextBox_Normal}" ></TextBox>
                    <Label Content="压力值：" Style="{DynamicResource DataDetailFieldTitle_4Words}" ></Label>
                    <TextBox Text="{Binding JCY_QM_Pressure_Begin}" Style="{DynamicResource TextBox_Short}" ></TextBox>
                    <Label Content="--" Style="{DynamicResource DataDetailFieldTitle_4Words}" Width="30"></Label>
                    <TextBox Text="{Binding JCY_QM_Pressure_End}" Style="{DynamicResource TextBox_Short}" ></TextBox>
                    <Label Content="泄漏值：" Style="{DynamicResource DataDetailFieldTitle_4Words}" ></Label>
                    <TextBox Text="{Binding JCY_QM_Leak_Begin}" Style="{DynamicResource TextBox_Short}" ></TextBox>
                    <Label Content="--" Style="{DynamicResource DataDetailFieldTitle_4Words}" Width="25"></Label>
                    <TextBox Text="{Binding JCY_QM_Leak_End}" Style="{DynamicResource TextBox_Short}" ></TextBox>
                    <!--<Label Content="内密结果：" Style="{DynamicResource DataDetailFieldTitle_4Words}" FontWeight="Bold"></Label>
                        <ComboBox Width="80" FontSize="20" Text="{Binding JCY_NM_Result}" >
                            <ComboBoxItem  IsSelected="True" Content="全部"/>
                            <ComboBoxItem Content="OK" />
                            <ComboBoxItem Content="NG" />
                        </ComboBox>-->
                    <Label Content="检测结果：" Style="{DynamicResource DataDetailFieldTitle_4Words}"  FontWeight="Bold"  ></Label>
                    <ComboBox Width="80" FontSize="20" Text="{Binding JCY_QM_Result}" >
                        <ComboBoxItem  IsSelected="True" Content="全部"/>
                        <ComboBoxItem Content="OK" />
                        <ComboBoxItem Content="NG" />
                    </ComboBox>
                </WrapPanel>
            </Border>
            <Label Content="分零件：内密/外密" Style="{DynamicResource DataDetailTitle}" Height="40"></Label>
            <Border Style="{DynamicResource DataDetailContentBorder}" Width="950" Height="40">
                <WrapPanel Grid.Row="0" Grid.ColumnSpan="2" VerticalAlignment="Center" HorizontalAlignment="Right">
                    <Label Style="{DynamicResource DataDetailFieldTitle_4Words}" FontWeight="Bold" Content="内密结果：" ></Label>
                    <ComboBox Width="80" FontSize="20" Text="{Binding JCY_NM_Result}" >
                        <ComboBoxItem  IsSelected="True" Content="全部"/>
                        <ComboBoxItem Content="OK" />
                        <ComboBoxItem Content="NG" />
                    </ComboBox>
                    <Label Style="{DynamicResource DataDetailFieldTitle_4Words}" FontWeight="Bold" Content="外密结果：" ></Label>
                    <ComboBox Width="80" FontSize="20" Text="{Binding FLJ_WM_Result}" Margin="0,0,23,0" >
                        <ComboBoxItem  IsSelected="True" Content="全部"/>
                        <ComboBoxItem Content="OK" />
                        <ComboBoxItem Content="NG" />
                    </ComboBox>
                </WrapPanel>
            </Border>
            <Border Style="{DynamicResource DataDetailContentBorder}" Width="1113" Height="40">
                <WrapPanel VerticalAlignment="Center" >
                    <Button Style="{DynamicResource GreenButton}" Content="查询" Margin="410,2,0,0" Click="Button_Click"></Button>
                    <Button Style="{DynamicResource GrayButton}" Content="取消"  Margin="10,2,0,0" Click="Button_Click"></Button>
                    <Button Style="{DynamicResource BlueButton}" Content="重置" Margin="10,2,0,0" Click="Button_Click"></Button>
                </WrapPanel>
            </Border>
        </WrapPanel>
    </Grid>
</Window>
