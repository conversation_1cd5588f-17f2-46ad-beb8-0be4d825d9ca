﻿using System.Windows.Controls;
using ViewModels;

namespace WorkPieceDataCapture.Views
{
    /// <summary>
    /// WorkPieceDataCapture_RealTime.xaml 的交互逻辑
    /// </summary>
    public partial class uc_WorkPieceDataCapture_RealTime : UserControl
    {
        public uc_WorkPieceDataCapture_RealTime()
        {
            InitializeComponent();
        }
        /// <summary>
        /// DataGrid选择行事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void DataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            var vm = this.DataContext as vm_WorkPieceDatacapture_RealTime;
            if (vm != null && vm.RowSelectedCommand.CanExecute(null))
            {
                vm.RowSelectedCommand.Execute(this.GridProduceRecord.SelectedItem);
            }
        }

        private void Button_Click(object sender, System.Windows.RoutedEventArgs e)
        {
            var btn = sender as Button;
            if (btn == null || btn.Tag == null || btn.Content == null) return;
            var vm = this.DataContext as vm_WorkPieceDatacapture_RealTime;
            switch (btn.Tag.ToString())
            {
                case "path":    //根据显示的路径打开文件 
                    if (vm != null && vm.OpenFile.CanExecute(null)) vm.OpenFile.Execute(btn.Content.ToString());
                    break;
                case "ylqxt_newest":    //压力曲线图
                    if (vm != null && vm.PressureCurve.CanExecute(null)) vm.PressureCurve.Execute("Newest");
                    break;
                case "ylqxt_search":    //压力曲线图
                    if (vm != null && vm.PressureCurve.CanExecute(null)) vm.PressureCurve.Execute("Search");
                    break;
                default:
                    break;
            }

        }
    }
}
