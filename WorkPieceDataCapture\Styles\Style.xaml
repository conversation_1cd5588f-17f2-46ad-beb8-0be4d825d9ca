﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                   xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- 工具栏Button样式 -->
    <Style x:Key="ToolBarButton" TargetType="{x:Type Button}">
        <Setter Property="Background" Value="#015478" />
        <Setter Property="Height" Value="45"></Setter>
        <Setter Property="Width" Value="163"></Setter>
        <Setter Property="VerticalAlignment" Value="Center"></Setter>
        <Setter Property="Margin" Value="10,6,0,6"></Setter>
    </Style>
    <Style x:Key="ToolBarButtonText" TargetType="{x:Type TextBlock}">
        <Setter Property="FontSize" Value="18"></Setter>
        <Setter Property="Foreground" Value="White"></Setter>
        <Setter Property="VerticalAlignment" Value="Center"></Setter>
    </Style>
    <Style x:Key="ToolBarButtonPanel" TargetType="{x:Type WrapPanel}">
        <Setter Property="Background" Value="#015478"></Setter>
        <Setter Property="VerticalAlignment" Value="Center"></Setter>
        <Setter Property="Margin" Value="10,5"></Setter>
    </Style>
    <Style x:Key="ToolBarButtonImage" TargetType="{x:Type Image}">
        <Setter Property="Height" Value="25"></Setter>
    </Style>
    <!--Grid的通用样式-->
    <Style x:Key="GeneralGrid" TargetType="{x:Type Grid}">
        <Setter Property="Background" Value="#064471"></Setter>
        <!---->
    </Style>
    <!--Title标题栏的样式-->
    <Style x:Key="GridTitle" TargetType="{x:Type Grid}">
        <Setter Property="Background" Value="#053c79"></Setter>
    </Style>
    <!--WrapPanel的通用样式-->
    <Style x:Key="GeneralWrapPanel" TargetType="{x:Type WrapPanel}">
        <Setter Property="Background" Value="#063E5D"></Setter>
    </Style>
    <!--Group的标题样式-->
    <Style x:Key="GroupTitle" TargetType="{x:Type Label}">
        <Setter Property="FontWeight" Value="Bold"></Setter>
        <Setter Property="Background" Value="#4D82AD"></Setter>
        <Setter Property="Foreground" Value="White"></Setter>
        <Setter Property="FontSize" Value="16"></Setter>
        <Setter Property="VerticalContentAlignment" Value="Center"></Setter>
        <Setter Property="Margin" Value="3,0,3,0"></Setter>
    </Style>
    <Style x:Key="TableTitle" TargetType="{x:Type Label}">
        <Setter Property="FontWeight" Value="Bold"></Setter>
        <Setter Property="Background" Value="#4D82AD"></Setter>
        <Setter Property="Foreground" Value="White"></Setter>
        <Setter Property="FontSize" Value="13"></Setter>
        <Setter Property="VerticalContentAlignment" Value="Center"></Setter>
        <Setter Property="HorizontalContentAlignment" Value="Center"></Setter>
        <Setter Property="BorderThickness" Value="1"></Setter>
        <Setter Property="BorderBrush" Value="LightGray"></Setter>
    </Style>
    <Style x:Key="DataDetailTitle" TargetType="{x:Type Label}">
        <Setter Property="Width" Value="160"></Setter>
        <Setter Property="Height" Value="32"></Setter>
        <Setter Property="VerticalContentAlignment" Value="Center"></Setter>
        <Setter Property="HorizontalContentAlignment" Value="Center"></Setter>
        <Setter Property="Foreground" Value="White"></Setter>
        <Setter Property="FontWeight" Value="Bold"></Setter>
        <Setter Property="FontSize" Value="16"></Setter>
        <Setter Property="BorderThickness" Value="1"></Setter>
        <Setter Property="BorderBrush" Value="White"></Setter>
        <Setter Property="Margin" Value="3,1,0,0"></Setter>
    </Style>
    <Style x:Key="DataDetailContentBorder" TargetType="{x:Type Border}">
        <Setter Property="Width" Value="1752"></Setter>
        <Setter Property="Margin" Value="1,1,0,0"></Setter>
        <Setter Property="Height" Value="32"></Setter>
        <Setter Property="BorderThickness" Value="1"></Setter>
        <Setter Property="BorderBrush" Value="White"></Setter>
    </Style>
    <Style x:Key="DataDetailFieldTitle_Tiny" TargetType="{x:Type Label}">
        <Setter Property="FontSize" Value="16"></Setter>
        <Setter Property="Foreground" Value="White"></Setter>
        <Setter Property="HorizontalContentAlignment" Value="Right"></Setter>
        <Setter Property="VerticalContentAlignment" Value="Center"></Setter>
        <Setter Property="Width" Value="60"></Setter>
    </Style>
    <Style x:Key="DataDetailFieldTitle_4Words" TargetType="{x:Type Label}">
        <Setter Property="FontSize" Value="16"></Setter>
        <Setter Property="Foreground" Value="White"></Setter>
        <Setter Property="HorizontalContentAlignment" Value="Right"></Setter>
        <Setter Property="VerticalContentAlignment" Value="Center"></Setter>
        <Setter Property="Width" Value="100"></Setter>
    </Style>
    <Style x:Key="DataDetailFieldTitle_5Words" TargetType="{x:Type Label}">
        <Setter Property="FontSize" Value="16"></Setter>
        <Setter Property="Foreground" Value="White"></Setter>
        <Setter Property="HorizontalContentAlignment" Value="Right"></Setter>
        <Setter Property="VerticalContentAlignment" Value="Center"></Setter>
        <Setter Property="Width" Value="130"></Setter>
    </Style>
    <Style x:Key="DataDetailFieldTitle_6Words" TargetType="{x:Type Label}">
        <Setter Property="FontSize" Value="16"></Setter>
        <Setter Property="Foreground" Value="White"></Setter>
        <Setter Property="HorizontalContentAlignment" Value="Right"></Setter>
        <Setter Property="VerticalContentAlignment" Value="Center"></Setter>
        <Setter Property="Width" Value="160"></Setter>
    </Style>
    <Style x:Key="DataDetailFieldValue" TargetType="{x:Type Label}">
        <Setter Property="FontSize" Value="16"></Setter>
        <Setter Property="Foreground" Value="#81D3F8"></Setter>
        <Setter Property="FontWeight" Value="Bold"></Setter>
        <Setter Property="FontFamily" Value="Arial"></Setter>
        <Setter Property="VerticalContentAlignment" Value="Center"></Setter>
        <Setter Property="Width" Value="250"></Setter>
    </Style>
    <Style x:Key="DataDetailFieldValue_Tiny" TargetType="{x:Type Label}">
        <Setter Property="FontSize" Value="16"></Setter>
        <Setter Property="Foreground" Value="#81D3F8"></Setter>
        <Setter Property="FontWeight" Value="Bold"></Setter>
        <Setter Property="FontFamily" Value="Arial"></Setter>
        <Setter Property="VerticalContentAlignment" Value="Center"></Setter>
        <Setter Property="Width" Value="65"></Setter>
    </Style>
    <Style x:Key="DataDetailFieldValue_Short" TargetType="{x:Type Label}">
        <Setter Property="FontSize" Value="16"></Setter>
        <Setter Property="Foreground" Value="#81D3F8"></Setter>
        <Setter Property="FontWeight" Value="Bold"></Setter>
        <Setter Property="FontFamily" Value="Arial"></Setter>
        <Setter Property="VerticalContentAlignment" Value="Center"></Setter>
        <Setter Property="Width" Value="130"></Setter>
    </Style>
    <Style x:Key="DataDetailFieldValue_Long" TargetType="{x:Type Label}">
        <Setter Property="FontSize" Value="16"></Setter>
        <Setter Property="Foreground" Value="#81D3F8"></Setter>
        <Setter Property="FontWeight" Value="Bold"></Setter>
        <Setter Property="FontFamily" Value="Arial"></Setter>
        <Setter Property="VerticalContentAlignment" Value="Center"></Setter>
        <Setter Property="Width" Value="400"></Setter>
    </Style>
    <Style x:Key="DataDetailFieldValue_FilePath" TargetType="{x:Type Label}">
        <Setter Property="FontSize" Value="12"></Setter>
        <Setter Property="Foreground" Value="#81D3F8"></Setter>
        <Setter Property="FontWeight" Value="Bold"></Setter>
        <Setter Property="FontFamily" Value="Arial"></Setter>
        <Setter Property="VerticalContentAlignment" Value="Center"></Setter>
        <Setter Property="Width" Value="610"></Setter>
    </Style>
    <Style x:Key="DataDetailTestResult" TargetType="{x:Type Label}">
        <Setter Property="FontSize" Value="22"></Setter>
        <Setter Property="Foreground" Value="#81D3F8"></Setter>
        <Setter Property="FontWeight" Value="Bold"></Setter>
        <Setter Property="FontFamily" Value="Arial"></Setter>
        <Setter Property="VerticalContentAlignment" Value="Center"></Setter>
        <Setter Property="HorizontalContentAlignment" Value="Center"></Setter>
        <Setter Property="Width" Value="65"></Setter>
    </Style>
    <Style x:Key="TableTestValue" TargetType="{x:Type TextBlock}">
        <Setter Property="FontSize" Value="14"></Setter>
        <Setter Property="Foreground" Value="White"></Setter>
        <Setter Property="VerticalAlignment" Value="Center"></Setter>
        <Setter Property="HorizontalAlignment" Value="Center"></Setter>
    </Style>
    <Style x:Key="DetailFieldValue_Normal" TargetType="{x:Type TextBlock}">
        <Setter Property="FontSize" Value="16"></Setter>
        <Setter Property="Foreground" Value="#81D3F8"></Setter>
        <Setter Property="FontWeight" Value="Bold"></Setter>
        <Setter Property="FontFamily" Value="Arial"></Setter>
        <Setter Property="Width" Value="130"></Setter>
        <Setter Property="VerticalAlignment" Value="Center"></Setter>
    </Style>
    <Style x:Key="DetailFieldValue_Long" TargetType="{x:Type TextBlock}">
        <Setter Property="FontSize" Value="16"></Setter>
        <Setter Property="Foreground" Value="#81D3F8"></Setter>
        <Setter Property="FontWeight" Value="Bold"></Setter>
        <Setter Property="FontFamily" Value="Arial"></Setter>
        <Setter Property="Width" Value="180"></Setter>
        <Setter Property="VerticalAlignment" Value="Center"></Setter>
    </Style>
    <Style x:Key="GreenButton" TargetType="Button">
        <Setter Property="FontSize" Value="16"></Setter>
        <Setter Property="Foreground" Value="White"></Setter>
        <Setter Property="Background" Value="#01CD63"></Setter>
        <Setter Property="Width" Value="80"></Setter>
        <Setter Property="Height" Value="30"></Setter>
        <Setter Property="HorizontalAlignment" Value="Left" ></Setter>
        <Setter Property="Margin" Value="10,0,0,0" ></Setter>
    </Style>
    <Style x:Key="BlueButton" TargetType="Button">
        <Setter Property="FontSize" Value="16"></Setter>
        <Setter Property="Foreground" Value="White"></Setter>
        <Setter Property="Background" Value="#199BFE"></Setter>
        <Setter Property="Width" Value="80"></Setter>
        <Setter Property="Height" Value="30"></Setter>
        <Setter Property="HorizontalAlignment" Value="Left" ></Setter>
        <Setter Property="Margin" Value="10,0,0,0" ></Setter>
    </Style>
    <Style x:Key="GrayButton" TargetType="Button">
        <Setter Property="FontSize" Value="16"></Setter>
        <Setter Property="Foreground" Value="White"></Setter>
        <Setter Property="Background" Value="#728EB1"></Setter>
        <Setter Property="Width" Value="80"></Setter>
        <Setter Property="Height" Value="30"></Setter>
        <Setter Property="HorizontalAlignment" Value="Left" ></Setter>
        <Setter Property="Margin" Value="10,0,0,0" ></Setter>
    </Style>
    <Style x:Key="FileLinkButton" TargetType="Button">
        <Setter Property="FontSize" Value="16"></Setter>
        <Setter Property="Foreground" Value="#81D3F8"></Setter>
        <Setter Property="Background" Value="Transparent"></Setter>
        <Setter Property="HorizontalAlignment" Value="Left" ></Setter>
        <Setter Property="HorizontalContentAlignment" Value="Left" ></Setter>
    </Style>
    <Style x:Key="TextBox_Normal" TargetType="TextBox">
        <Setter Property="Background" Value="#015478"></Setter>
        <Setter Property="Foreground" Value="#81D3F8"></Setter>
        <Setter Property="FontSize" Value="16"></Setter>
        <Setter Property="FontWeight" Value="Bold"></Setter>
        <Setter Property="VerticalContentAlignment" Value="Center"></Setter>
        <Setter Property="Width" Value="150"></Setter>
    </Style>
    <Style x:Key="TextBox_Short" TargetType="TextBox">
        <Setter Property="Background" Value="#015478"></Setter>
        <Setter Property="Foreground" Value="#81D3F8"></Setter>
        <Setter Property="FontSize" Value="16"></Setter>
        <Setter Property="FontWeight" Value="Bold"></Setter>
        <Setter Property="VerticalContentAlignment" Value="Center"></Setter>
        <Setter Property="Width" Value="60"></Setter>
    </Style>
    <!-- 定义其他样式 -->

</ResourceDictionary>