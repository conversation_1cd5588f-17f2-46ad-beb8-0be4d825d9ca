﻿using System.Windows;
using System.Windows.Controls;
using ViewModels;
namespace WorkPieceDataCapture.Views
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : Window
    {
        public MainWindow()
        {
            InitializeComponent();
        }

        private void Menu_Click(object sender, RoutedEventArgs e)
        {
            var vm = this.DataContext as vm_MainWindow;
            if (vm != null && vm.MenuSelectCommand.CanExecute(null))
            {
                vm.MenuSelectCommand.Execute((sender as Button).Tag.ToString());
            }
        }

        private void WrapPanel_PreviewMouseDown(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            Application.Current.Shutdown();
        }
    }
}