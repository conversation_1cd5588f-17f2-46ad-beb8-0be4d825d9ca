<Window x:Class="WorkPieceDataCapture.Views.CoordinateDataTestWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="坐标数据测试" Height="300" Width="500">
    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <TextBlock Grid.Row="0" Text="输入坐标数据JSON字符串:" Margin="0,0,0,5"/>

        <TextBox Grid.Row="1" x:Name="txtCoordinateData" AcceptsReturn="True" TextWrapping="Wrap"
                 VerticalScrollBarVisibility="Auto" Margin="0,0,0,10"
                 Text='[{"X":1,"Y":10},{"X":2,"Y":15},{"X":3,"Y":13},{"X":4,"Y":20},{"X":5,"Y":18},{"X":6,"Y":25},{"X":7,"Y":22},{"X":8,"Y":30},{"X":9,"Y":35},{"X":10,"Y":32}]'/>

        <Button Grid.Row="2" Content="显示图表" Click="ShowChart_Click" Width="100" Height="30"/>
    </Grid>
</Window>
