﻿<UserControl x:Class="WorkPieceDataCapture.Views.uc_WorkPieceDataCapture_RealTime" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
xmlns:d="http://schemas.microsoft.com/expression/blend/2008" mc:Ignorable="d" d:DesignHeight="1000" d:DesignWidth="1920">
    <Viewbox Stretch="Fill">
        <Grid Style="{DynamicResource GeneralGrid}" Height="1080" Width="1920">
            <Grid.RowDefinitions>
                <RowDefinition Height="30"></RowDefinition>
                <RowDefinition Height="380"></RowDefinition>
                <RowDefinition Height="30"></RowDefinition>
                <RowDefinition Height="*"></RowDefinition>
                <RowDefinition Height="50"></RowDefinition>
            </Grid.RowDefinitions>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="720"></ColumnDefinition>
                <ColumnDefinition Width="*"></ColumnDefinition>
            </Grid.ColumnDefinitions>
            <Label Content="    最新实时数据" Grid.ColumnSpan="2" Style="{DynamicResource GroupTitle}"></Label>
            <WrapPanel Grid.Column="1" HorizontalAlignment="Right">
                <Label Content="24小时产量：" Style="{DynamicResource DataDetailFieldTitle_4Words}" ></Label>
                <Label Content="{Binding dataModel_Summary.production24Hour}" Style="{DynamicResource DataDetailFieldValue_Tiny}" FontSize="20" ></Label>
                <Label Content="OK：" Style="{DynamicResource DataDetailFieldTitle_Tiny}" ></Label>
                <Label Content="{Binding dataModel_Summary.OK24Hour}" Style="{DynamicResource DataDetailFieldValue_Tiny}" Foreground="#95F204" FontSize="20"></Label>
                <Label Content="NG：" Style="{DynamicResource DataDetailFieldTitle_Tiny}" ></Label>
                <Label Content="{Binding dataModel_Summary.NG24Hour}" Style="{DynamicResource DataDetailFieldTitle_tiny}" Width="50" Foreground="Red" FontSize="20" FontWeight="Bold"></Label>
                <Label Content="良品率：" Style="{DynamicResource DataDetailFieldTitle_Tiny}" ></Label>
                <Label Content="{Binding dataModel_Summary.OKRate24Hour}" Style="{DynamicResource DataDetailFieldTitle_4Words}" Foreground="#FFFF00" FontSize="20" FontWeight="Bold"></Label>
                <Label Content="今日产量：" Style="{DynamicResource DataDetailFieldTitle_4Words}" ></Label>
                <Label Content="{Binding dataModel_Summary.productionToday}" Style="{DynamicResource DataDetailFieldValue_Tiny}" FontSize="20" ></Label>
                <Label Content="OK：" Style="{DynamicResource DataDetailFieldTitle_Tiny}" ></Label>
                <Label Content="{Binding dataModel_Summary.OKToday}" Style="{DynamicResource DataDetailFieldValue_Tiny}" Foreground="#95F204" FontSize="20"></Label>
                <Label Content="NG：" Style="{DynamicResource DataDetailFieldTitle_Tiny}" ></Label>
                <Label Content="{Binding dataModel_Summary.NGToday}" Style="{DynamicResource DataDetailFieldValue_Tiny}" Width="50" Foreground="Red" FontSize="20" FontWeight="Bold"></Label>
                <Label Content="良品率：" Style="{DynamicResource DataDetailFieldTitle_Tiny}" ></Label>
                <Label Content="{Binding dataModel_Summary.OKRateToday}" Style="{DynamicResource DataDetailFieldTitle_4Words}" Foreground="#FFFF00" FontSize="20" FontWeight="Bold"></Label>
            </WrapPanel>
            <WrapPanel  Grid.Row="1" Grid.ColumnSpan="2" Width="1920" Background="#015478">
                <Label Content="总成" Style="{DynamicResource DataDetailTitle}"></Label>
                <Border Style="{DynamicResource DataDetailContentBorder}">
                    <WrapPanel>
                        <Label Content="标识码：" Style="{DynamicResource DataDetailFieldTitle_4Words}" ></Label>
                        <Label Content="{Binding dataModel_Newest.ZC_SerialNo}" Style="{DynamicResource DataDetailFieldValue}"></Label>
                        <Label Content="总成型号：" Style="{DynamicResource DataDetailFieldTitle_4Words}"></Label>
                        <Label Content="{Binding dataModel_Newest.ZC_Model}" Style="{DynamicResource DataDetailFieldValue}"></Label>
                        <Label Content="激光打标码：" Style="{DynamicResource DataDetailFieldTitle_6Words}"></Label>
                        <Label Content="{Binding dataModel_Newest.ZC_LaserNo}" Style="{DynamicResource DataDetailFieldValue}"></Label>
                        <Label Content="生产日期：" Style="{DynamicResource DataDetailFieldTitle_4Words}" Margin="100,0,0,0"></Label>
                        <Label Content="{Binding dataModel_Newest.ZC_Time,StringFormat=yyyy-MM-dd}" Style="{DynamicResource DataDetailFieldValue_Short}" Width="200" ContentStringFormat=""></Label>
                        <Label Content="综合检测结果：" Style="{DynamicResource DataDetailFieldTitle_6Words}"  FontWeight="Bold"></Label>
                        <TextBlock Text="{Binding dataModel_Newest.ZC_Result}"   FontSize="30" FontFamily="Arial" Height="50" Margin="10,-1" FontWeight="Bold" Width="60">
                            <TextBlock.Style>
                                <Style TargetType="{x:Type TextBlock}">
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding dataModel_Newest.ZC_Result}" Value="OK">
                                            <Setter Property="Foreground" Value="#00FFFF" />
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding dataModel_Newest.ZC_Result}" Value="NG">
                                            <Setter Property="Foreground" Value="Red" />
                                        </DataTrigger>
                                        <!-- 其他触发器 -->
                                    </Style.Triggers>
                                </Style>
                            </TextBlock.Style>
                            <Run Text="{Binding dataModel_Newest.ZC_Result}" />
                        </TextBlock>
                    </WrapPanel>
                </Border>
                <Label Content="分零件：主体" Style="{DynamicResource DataDetailTitle}"></Label>
                <Border Style="{DynamicResource DataDetailContentBorder}">
                    <WrapPanel >
                        <Label Content="型号码：" Style="{DynamicResource DataDetailFieldTitle_4Words}"></Label>
                        <Label Content="{Binding dataModel_Newest.FLJ_ZT_Model}" Style="{DynamicResource DataDetailFieldValue}"></Label>
                        <Label Content="物料编码：" Style="{DynamicResource DataDetailFieldTitle_4Words}"></Label>
                        <Label Content="{Binding dataModel_Newest.FLJ_ZT_Material}" Style="{DynamicResource DataDetailFieldValue}"></Label>
                        <Label Content="异物位移上限值：" Style="{DynamicResource DataDetailFieldTitle_6Words}"></Label>
                        <Label Content="{Binding dataModel_Newest.FLJ_ZT_Upper}" Style="{DynamicResource DataDetailFieldValue_Tiny}"></Label>
                        <Label Content="异物位移下限值：" Style="{DynamicResource DataDetailFieldTitle_6Words}"></Label>
                        <Label Content="{Binding dataModel_Newest.FLJ_ZT_Lower}" Style="{DynamicResource DataDetailFieldValue_Tiny}"></Label>
                        <Label Content="异物位移检测值：" Style="{DynamicResource DataDetailFieldTitle_6Words}"></Label>
                        <Label Content="{Binding dataModel_Newest.FLJ_ZT_Value}" Style="{DynamicResource DataDetailFieldValue_Tiny}"></Label>
                        <Label Content="检测结果：" Style="{DynamicResource DataDetailFieldTitle_4Words}" FontWeight="Bold" Margin="195,0,0,0"></Label>
                        <!--<Label Content="{Binding dataModel_Newest.FLJ_ZT_Result}" Style="{DynamicResource DataDetailTestResult}"></Label>-->
                        <TextBlock FontWeight="Bold" FontSize="22" HorizontalAlignment="Center" VerticalAlignment="Center" Width="65" TextAlignment="Center">
                            <TextBlock.Style>
                                <Style TargetType="{x:Type TextBlock}">
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding dataModel_Newest.FLJ_ZT_Result}" Value="OK">
                                            <Setter Property="Foreground" Value="#00FFFF" />
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding dataModel_Newest.FLJ_ZT_Result}" Value="NG">
                                            <Setter Property="Foreground" Value="Red" />
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding dataModel_Newest.FLJ_ZT_Result}" Value="NA">
                                            <Setter Property="Foreground" Value="Gray" />
                                        </DataTrigger>
                                        <!-- 其他触发器 -->
                                    </Style.Triggers>
                                </Style>
                            </TextBlock.Style>
                            <Run Text="{Binding dataModel_Newest.FLJ_ZT_Result}" />
                        </TextBlock>
                    </WrapPanel>
                </Border>
                <Label Content="分零件：一次圈" Style="{DynamicResource DataDetailTitle}"></Label>
                <Border Style="{DynamicResource DataDetailContentBorder}">
                    <WrapPanel >
                        <Label Content="型号码：" Style="{DynamicResource DataDetailFieldTitle_4Words}"></Label>
                        <Label Content="{Binding dataModel_Newest.FLJ_YCQ_Model}" Style="{DynamicResource DataDetailFieldValue}"></Label>
                        <Label Content="物料编码：" Style="{DynamicResource DataDetailFieldTitle_4Words}"></Label>
                        <Label Content="{Binding dataModel_Newest.FLJ_YCQ_Material}" Style="{DynamicResource DataDetailFieldValue}"></Label>
                        <Label Content="位移检测上限值：" Style="{DynamicResource DataDetailFieldTitle_6Words}" Visibility="Collapsed"></Label>
                        <Label Content="{Binding dataModel_Newest.FLJ_YCQ_Upper}" Style="{DynamicResource DataDetailFieldValue_Tiny}" Visibility="Collapsed"></Label>
                        <Label Content="位移检测下限值：" Style="{DynamicResource DataDetailFieldTitle_6Words}" Visibility="Collapsed"></Label>
                        <Label Content="{Binding dataModel_Newest.FLJ_YCQ_Lower}" Style="{DynamicResource DataDetailFieldValue_Tiny}" Visibility="Collapsed"></Label>
                        <Label Content="位移检测值：" Style="{DynamicResource DataDetailFieldTitle_6Words}" Visibility="Collapsed"></Label>
                        <Label Content="{Binding dataModel_Newest.FLJ_YCQ_Value}" Style="{DynamicResource DataDetailFieldValue_Tiny}" Visibility="Collapsed"></Label>
                        <Label Content="检测结果：" Style="{DynamicResource DataDetailFieldTitle_4Words}" FontWeight="Bold" Margin="45,0,0,0" Visibility="Collapsed"></Label>
                        <!--<Label Content="{Binding dataModel_Newest.FLJ_YCQ_Result}" Style="{DynamicResource DataDetailTestResult}" Visibility="Collapsed"></Label>-->
                        <TextBlock FontWeight="Bold" FontSize="22" HorizontalAlignment="Center" VerticalAlignment="Center" Width="65" TextAlignment="Center" Visibility="Collapsed">
                            <TextBlock.Style>
                                <Style TargetType="{x:Type TextBlock}">
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding dataModel_Newest.FLJ_YCQ_Result}" Value="OK">
                                            <Setter Property="Foreground" Value="#00FFFF" />
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding dataModel_Newest.FLJ_YCQ_Result}" Value="NG">
                                            <Setter Property="Foreground" Value="Red" />
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding dataModel_Newest.FLJ_YCQ_Result}" Value="NA">
                                            <Setter Property="Foreground" Value="Gray" />
                                        </DataTrigger>
                                        <!-- 其他触发器 -->
                                    </Style.Triggers>
                                </Style>
                            </TextBlock.Style>
                            <Run Text="{Binding dataModel_Newest.FLJ_YCQ_Result}" />
                        </TextBlock>
                    </WrapPanel>
                </Border>
                <Label Content="分零件：隔离环" Style="{DynamicResource DataDetailTitle}"></Label>
                <Border Style="{DynamicResource DataDetailContentBorder}">
                    <WrapPanel >
                        <Label Content="型号码：" Style="{DynamicResource DataDetailFieldTitle_4Words}"></Label>
                        <Label Content="{Binding dataModel_Newest.FLJ_GLH_Model}" Style="{DynamicResource DataDetailFieldValue}"></Label>
                        <Label Content="物料编码：" Style="{DynamicResource DataDetailFieldTitle_4Words}"></Label>
                        <Label Content="{Binding dataModel_Newest.FLJ_GLH_Material}" Style="{DynamicResource DataDetailFieldValue}"></Label>
                        <Label Content="位移检测上限值：" Style="{DynamicResource DataDetailFieldTitle_6Words}"></Label>
                        <Label Content="{Binding dataModel_Newest.FLJ_GLH_Upper}" Style="{DynamicResource DataDetailFieldValue_Tiny}"></Label>
                        <Label Content="位移检测下限值：" Style="{DynamicResource DataDetailFieldTitle_6Words}"></Label>
                        <Label Content="{Binding dataModel_Newest.FLJ_GLH_Lower}" Style="{DynamicResource DataDetailFieldValue_Tiny}"></Label>
                        <Label Content="位移检测值：" Style="{DynamicResource DataDetailFieldTitle_6Words}"></Label>
                        <Label Content="{Binding dataModel_Newest.FLJ_GLH_Value}" Style="{DynamicResource DataDetailFieldValue_Tiny}"></Label>
                        <Label Content="检测结果：" Style="{DynamicResource DataDetailFieldTitle_4Words}" FontWeight="Bold" Margin="195,0,0,0"></Label>
                        <!--<Label Content="{Binding dataModel_Newest.FLJ_GLH_Result}" Style="{DynamicResource DataDetailTestResult}"></Label>-->
                        <TextBlock FontWeight="Bold" FontSize="22" HorizontalAlignment="Center" VerticalAlignment="Center" Width="65" TextAlignment="Center">
                            <TextBlock.Style>
                                <Style TargetType="{x:Type TextBlock}">
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding dataModel_Newest.FLJ_GLH_Result}" Value="OK">
                                            <Setter Property="Foreground" Value="#00FFFF" />
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding dataModel_Newest.FLJ_GLH_Result}" Value="NG">
                                            <Setter Property="Foreground" Value="Red" />
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding dataModel_Newest.FLJ_GLH_Result}" Value="NA">
                                            <Setter Property="Foreground" Value="Gray" />
                                        </DataTrigger>
                                        <!-- 其他触发器 -->
                                    </Style.Triggers>
                                </Style>
                            </TextBlock.Style>
                            <Run Text="{Binding dataModel_Newest.FLJ_GLH_Result}" />
                        </TextBlock>
                    </WrapPanel>
                </Border>
                <Label Content="分零件：二次圈" Style="{DynamicResource DataDetailTitle}"></Label>
                <Border Style="{DynamicResource DataDetailContentBorder}">
                    <WrapPanel >
                        <Label Content="型号码：" Style="{DynamicResource DataDetailFieldTitle_4Words}"></Label>
                        <Label Content="{Binding dataModel_Newest.FLJ_ECQ_Model}" Style="{DynamicResource DataDetailFieldValue}"></Label>
                        <Label Content="物料编码：" Style="{DynamicResource DataDetailFieldTitle_4Words}"></Label>
                        <Label Content="{Binding dataModel_Newest.FLJ_ECQ_Material}" Style="{DynamicResource DataDetailFieldValue}"></Label>
                        <Label Content="位移检测上限值：" Style="{DynamicResource DataDetailFieldTitle_6Words}"></Label>
                        <Label Content="{Binding dataModel_Newest.FLJ_ECQ_Upper}" Style="{DynamicResource DataDetailFieldValue_Tiny}"></Label>
                        <Label Content="位移检测下限值：" Style="{DynamicResource DataDetailFieldTitle_6Words}"></Label>
                        <Label Content="{Binding dataModel_Newest.FLJ_ECQ_Lower}" Style="{DynamicResource DataDetailFieldValue_Tiny}"></Label>
                        <Label Content="位移检测值：" Style="{DynamicResource DataDetailFieldTitle_6Words}"></Label>
                        <Label Content="{Binding dataModel_Newest.FLJ_ECQ_Value}" Style="{DynamicResource DataDetailFieldValue_Tiny}"></Label>
                        <Label Content="检测结果：" Style="{DynamicResource DataDetailFieldTitle_4Words}" FontWeight="Bold" Margin="195,0,0,0"></Label>
                        <!--<Label Content="{Binding dataModel_Newest.FLJ_ECQ_Result}" Style="{DynamicResource DataDetailTestResult}"></Label>-->
                        <TextBlock FontWeight="Bold" FontSize="22" HorizontalAlignment="Center" VerticalAlignment="Center" Width="65" TextAlignment="Center">
                            <TextBlock.Style>
                                <Style TargetType="{x:Type TextBlock}">
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding dataModel_Newest.FLJ_ECQ_Result}" Value="OK">
                                            <Setter Property="Foreground" Value="#00FFFF" />
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding dataModel_Newest.FLJ_ECQ_Result}" Value="NG">
                                            <Setter Property="Foreground" Value="Red" />
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding dataModel_Newest.FLJ_ECQ_Result}" Value="NA">
                                            <Setter Property="Foreground" Value="Gray" />
                                        </DataTrigger>
                                        <!-- 其他触发器 -->
                                    </Style.Triggers>
                                </Style>
                            </TextBlock.Style>
                            <Run Text="{Binding dataModel_Newest.FLJ_ECQ_Result}" />
                        </TextBlock>
                    </WrapPanel>
                </Border>
                <Label Content="分零件：安全环" Style="{DynamicResource DataDetailTitle}"></Label>
                <Border Style="{DynamicResource DataDetailContentBorder}">
                    <WrapPanel >
                        <Label Content="型号码：" Style="{DynamicResource DataDetailFieldTitle_4Words}"></Label>
                        <Label Content="{Binding dataModel_Newest.FLJ_KZH_Model}" Style="{DynamicResource DataDetailFieldValue_Short}"></Label>
                        <Label Content="物料编码：" Style="{DynamicResource DataDetailFieldTitle_4Words}"></Label>
                        <Label Content="{Binding dataModel_Newest.FLJ_KZH_Material}" Style="{DynamicResource DataDetailFieldValue}"></Label>
                        <Label Content="位移检测上限值：" Style="{DynamicResource DataDetailFieldTitle_6Words}"></Label>
                        <Label Content="{Binding dataModel_Newest.FLJ_KZH_Upper}" Style="{DynamicResource DataDetailFieldValue_Tiny}"></Label>
                        <Label Content="位移检测下限值：" Style="{DynamicResource DataDetailFieldTitle_6Words}"></Label>
                        <Label Content="{Binding dataModel_Newest.FLJ_KZH_Lower}" Style="{DynamicResource DataDetailFieldValue_Tiny}"></Label>
                        <Label Content="位移检测值：" Style="{DynamicResource DataDetailFieldTitle_4Words}"></Label>
                        <Label Content="{Binding dataModel_Newest.FLJ_KZH_Value}" Style="{DynamicResource DataDetailFieldValue_Tiny}"></Label>
                        <Label Content="压力检测：" Style="{DynamicResource DataDetailFieldTitle_4Words}" Margin="15,0,0,0"></Label>
                        <TextBlock FontWeight="Bold" FontSize="22" HorizontalAlignment="Center" VerticalAlignment="Center" Width="65" TextAlignment="Center">
                            <TextBlock.Style>
                                <Style TargetType="{x:Type TextBlock}">
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding dataModel_Newest.FLJ_KZH_PressureResult}" Value="OK">
                                            <Setter Property="Foreground" Value="#00FFFF" />
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding dataModel_Newest.FLJ_KZH_PressureResult}" Value="NG">
                                            <Setter Property="Foreground" Value="Red" />
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding dataModel_Newest.FLJ_KZH_PressureResult}" Value="NA">
                                            <Setter Property="Foreground" Value="Gray" />
                                        </DataTrigger>
                                        <!-- 其他触发器 -->
                                    </Style.Triggers>
                                </Style>
                            </TextBlock.Style>
                            <Run Text="{Binding dataModel_Newest.FLJ_KZH_PressureResult}" />
                        </TextBlock>
                        <Button Content="压力曲线图" Tag="ylqxt_newest"  Style="{DynamicResource FileLinkButton}" Width="85" Height="24" Click="Button_Click" ></Button>
                        <Label Content="检测结果：" Style="{DynamicResource DataDetailFieldTitle_4Words}" FontWeight="Bold" Margin="110,0,0,0"></Label>
                        <!--<Label Content="{Binding dataModel_Newest.FLJ_KZH_Result}" Style="{DynamicResource DataDetailTestResult}"></Label>-->
                        <TextBlock FontWeight="Bold" FontSize="22" HorizontalAlignment="Center" VerticalAlignment="Center" Width="65" TextAlignment="Center">
                            <TextBlock.Style>
                                <Style TargetType="{x:Type TextBlock}">
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding dataModel_Newest.FLJ_KZH_Result}" Value="OK">
                                            <Setter Property="Foreground" Value="#00FFFF" />
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding dataModel_Newest.FLJ_KZH_Result}" Value="NG">
                                            <Setter Property="Foreground" Value="Red" />
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding dataModel_Newest.FLJ_KZH_Result}" Value="NA">
                                            <Setter Property="Foreground" Value="Gray" />
                                        </DataTrigger>
                                        <!-- 其他触发器 -->
                                    </Style.Triggers>
                                </Style>
                            </TextBlock.Style>
                            <Run Text="{Binding dataModel_Newest.FLJ_KZH_Result}" />
                        </TextBlock>
                    </WrapPanel>
                </Border>
                <Label Content="分零件：锁扣" Style="{DynamicResource DataDetailTitle}" ></Label>
                <Border Style="{DynamicResource DataDetailContentBorder}" >
                    <WrapPanel>
                        <Label Content="型号码：" Style="{DynamicResource DataDetailFieldTitle_4Words}"></Label>
                        <Label Content="{Binding dataModel_Newest.FLJ_SK_Model}" Style="{DynamicResource DataDetailFieldValue}"></Label>
                        <Label Content="物料编码：" Style="{DynamicResource DataDetailFieldTitle_4Words}"></Label>
                        <Label Content="{Binding dataModel_Newest.FLJ_SK_Material}" Style="{DynamicResource DataDetailFieldValue}"></Label>
                        <Label Content="相机存储路径：" Style="{DynamicResource DataDetailFieldTitle_6Words}"></Label>
                        <Button Content="{Binding dataModel_Newest.FLJ_SK_Photo}" Tag="path"  Style="{DynamicResource FileLinkButton}" Width="640" Height="24" Click="Button_Click"></Button>
                        <Label Content="检测结果：" Style="{DynamicResource DataDetailFieldTitle_4Words}" FontWeight="Bold" HorizontalAlignment="Left" Margin="69,0,0,0"></Label>
                        <!--<Label Content="{Binding dataModel_Newest.FLJ_SK_Result}" Style="{DynamicResource DataDetailTestResult}" HorizontalAlignment="Right" Margin="0,0,15,0" ></Label>-->
                        <TextBlock FontWeight="Bold" FontSize="22" HorizontalAlignment="Center" VerticalAlignment="Center" Width="65" TextAlignment="Center">
                            <TextBlock.Style>
                                <Style TargetType="{x:Type TextBlock}">
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding dataModel_Newest.FLJ_SK_Result}" Value="OK">
                                            <Setter Property="Foreground" Value="#00FFFF" />
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding dataModel_Newest.FLJ_SK_Result}" Value="NG">
                                            <Setter Property="Foreground" Value="Red" />
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding dataModel_Newest.FLJ_SK_Result}" Value="NA">
                                            <Setter Property="Foreground" Value="Gray" />
                                        </DataTrigger>
                                        <!-- 其他触发器 -->
                                    </Style.Triggers>
                                </Style>
                            </TextBlock.Style>
                            <Run Text="{Binding dataModel_Newest.FLJ_SK_Result}" />
                        </TextBlock>
                    </WrapPanel>
                </Border>
                <Label Content="检测仪：内密" Style="{DynamicResource DataDetailTitle}"></Label>
                <Border Style="{DynamicResource DataDetailContentBorder}" >
                    <WrapPanel>
                        <Label Content="相机1存储路径：" Style="{DynamicResource DataDetailFieldTitle_6Words}"></Label>
                        <Button Content="{Binding dataModel_Newest.JCY_NM_Photo1}" Tag="path" Style="{DynamicResource FileLinkButton}" Width="540" Height="24" Click="Button_Click"></Button>
                        <Label Content="相机2存储路径：" Style="{DynamicResource DataDetailFieldTitle_6Words}"></Label>
                        <Button Content="{Binding dataModel_Newest.JCY_NM_Photo2}" Tag="path" Style="{DynamicResource FileLinkButton}" Width="640" Height="24" Click="Button_Click"></Button>
                        <Label Content="相机检测结果：" Style="{DynamicResource DataDetailFieldTitle_6Words}" FontWeight="Bold" HorizontalAlignment="Left" Margin="10,0,0,0"  ></Label>
                        <!--<Label Content="{Binding dataModel_Newest.JCY_NM_Result}" Style="{DynamicResource DataDetailTestResult}" HorizontalAlignment="Right" Margin="0,0,15,0" ></Label>-->
                        <TextBlock FontWeight="Bold" FontSize="22" HorizontalAlignment="Center" VerticalAlignment="Center" Width="65" TextAlignment="Center">
                            <TextBlock.Style>
                                <Style TargetType="{x:Type TextBlock}">
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding dataModel_Newest.JCY_NM_Result}" Value="OK">
                                            <Setter Property="Foreground" Value="#00FFFF" />
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding dataModel_Newest.JCY_NM_Result}" Value="NG">
                                            <Setter Property="Foreground" Value="Red" />
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding dataModel_Newest.JCY_NM_Result}" Value="NA">
                                            <Setter Property="Foreground" Value="Gray" />
                                        </DataTrigger>
                                        <!-- 其他触发器 -->
                                    </Style.Triggers>
                                </Style>
                            </TextBlock.Style>
                            <Run Text="{Binding dataModel_Newest.JCY_NM_Result}" />
                        </TextBlock>
                    </WrapPanel>
                </Border>
                <Label Content="分零件：外密" Style="{DynamicResource DataDetailTitle}" Height="60" ></Label>
                <Border Style="{DynamicResource DataDetailContentBorder}" Height="60" >
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition></RowDefinition>
                            <RowDefinition></RowDefinition>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"></ColumnDefinition>
                            <ColumnDefinition Width="180"></ColumnDefinition>
                        </Grid.ColumnDefinitions>
                        <WrapPanel>
                            <Label Content="型号码：" Style="{DynamicResource DataDetailFieldTitle_4Words}"></Label>
                            <Label Content="{Binding dataModel_Newest.FLJ_WM_Model}" Style="{DynamicResource DataDetailFieldValue}" ></Label>
                            <Label Content="物料编码：" Style="{DynamicResource DataDetailFieldTitle_4Words}"></Label>
                            <Label Content="{Binding dataModel_Newest.FLJ_WM_Material}" Style="{DynamicResource DataDetailFieldValue_Long}"></Label>
                        </WrapPanel>
                        <WrapPanel Grid.Row="1">
                            <Label Content="相机1存储路径：" Style="{DynamicResource DataDetailFieldTitle_6Words}"></Label>
                            <Button Content="{Binding dataModel_Newest.FLJ_WM_Photo1}" Tag="path" Style="{DynamicResource FileLinkButton}" Width="540" Height="24" Click="Button_Click" Background="Transparent" Foreground="#81D3F8" HorizontalContentAlignment="Left"></Button>
                            <Label Content="相机2存储路径：" Style="{DynamicResource DataDetailFieldTitle_6Words}"></Label>
                            <Button Content="{Binding dataModel_Newest.FLJ_WM_Photo2}" Tag="path" Style="{DynamicResource FileLinkButton}" Width="640" Height="24" Click="Button_Click" Background="Transparent" Foreground="#81D3F8" HorizontalContentAlignment="Left"></Button>
                        </WrapPanel>
                        <WrapPanel Grid.RowSpan="2" Grid.Column="1" VerticalAlignment="Center">
                            <Label Content="检测结果：" Style="{DynamicResource DataDetailFieldTitle_4Words}" FontWeight="Bold" HorizontalAlignment="Left" ></Label>
                            <!--<Label Content="{Binding dataModel_Newest.FLJ_WM_Result}" Style="{DynamicResource DataDetailTestResult}"  ></Label>-->
                            <TextBlock FontWeight="Bold" FontSize="22" HorizontalAlignment="Center" VerticalAlignment="Center" Width="65" TextAlignment="Center">
                                <TextBlock.Style>
                                    <Style TargetType="{x:Type TextBlock}">
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding dataModel_Newest.FLJ_WM_Result}" Value="OK">
                                                <Setter Property="Foreground" Value="#00FFFF" />
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding dataModel_Newest.FLJ_WM_Result}" Value="NG">
                                                <Setter Property="Foreground" Value="Red" />
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding dataModel_Newest.FLJ_WM_Result}" Value="NA">
                                                <Setter Property="Foreground" Value="Gray" />
                                            </DataTrigger>
                                            <!-- 其他触发器 -->
                                        </Style.Triggers>
                                    </Style>
                                </TextBlock.Style>
                            <Run Text="{Binding dataModel_Newest.FLJ_WM_Result}" />
                            </TextBlock>
                        </WrapPanel>
                    </Grid>
                </Border>
                <Label Content="检测仪：内密气密" Style="{DynamicResource DataDetailTitle}"></Label>
                <Border Style="{DynamicResource DataDetailContentBorder}" >
                    <WrapPanel>
                        <Label Content="气密检测程序号：" Style="{DynamicResource DataDetailFieldTitle_6Words}"></Label>
                        <Label Content="{Binding dataModel_Newest.JCY_QM_Program}" Style="{DynamicResource DataDetailFieldValue_Short}"></Label>
                        <Label Content="气密检测压力值：" Style="{DynamicResource DataDetailFieldTitle_6Words}"></Label>
                        <Label Content="{Binding dataModel_Newest.JCY_QM_Pressure}" Style="{DynamicResource DataDetailFieldValue_Short}"></Label>
                        <Label Content="气密检测泄漏值：" Style="{DynamicResource DataDetailFieldTitle_6Words}"></Label>
                        <Label Content="{Binding dataModel_Newest.JCY_QM_Leak}" Style="{DynamicResource DataDetailFieldValue_Short}"></Label>
                        <Label Content="气密检测结果：" Style="{DynamicResource DataDetailFieldTitle_6Words}" FontWeight="Bold" Margin="640,0,0,0"></Label>
                        <!--<Label Content="{Binding dataModel_Newest.JCY_QM_Result}" Style="{DynamicResource DataDetailTestResult}"></Label>-->
                        <TextBlock FontWeight="Bold" FontSize="22" HorizontalAlignment="Center" VerticalAlignment="Center" Width="65" TextAlignment="Center">
                            <TextBlock.Style>
                                <Style TargetType="{x:Type TextBlock}">
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding dataModel_Newest.JCY_QM_Result}" Value="OK">
                                            <Setter Property="Foreground" Value="#00FFFF" />
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding dataModel_Newest.JCY_QM_Result}" Value="NG">
                                            <Setter Property="Foreground" Value="Red" />
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding dataModel_Newest.JCY_QM_Result}" Value="NA">
                                            <Setter Property="Foreground" Value="Gray" />
                                        </DataTrigger>
                                        <!-- 其他触发器 -->
                                    </Style.Triggers>
                                </Style>
                            </TextBlock.Style>
                            <Run Text="{Binding dataModel_Newest.JCY_QM_Result}" />
                        </TextBlock>
                    </WrapPanel>
                </Border>

            </WrapPanel>
            <Label Content="    生产记录" Grid.Row="2" Style="{DynamicResource GroupTitle}"></Label>
            <WrapPanel  Grid.Row="3" Width="1920" Background="#015478">
                <DataGrid Name="GridProduceRecord" Background="#015478"  Height="635" ItemsSource="{Binding list_History}" Margin="5,0,5,0" HeadersVisibility="Column" SelectionMode="Single" IsReadOnly="True"  SelectionChanged="DataGrid_SelectionChanged" VerticalScrollBarVisibility="Hidden" CanUserAddRows="False" CanUserDeleteRows="False" CanUserResizeColumns="False" CanUserSortColumns="False" AutoGenerateColumns="False" AllowDrop="False">
                    <DataGrid.Resources>
                        <Style TargetType="DataGridCell">
                            <Setter Property="Height" Value="28"></Setter>
                            <Setter Property="BorderThickness" Value="0"></Setter>
                            <Setter Property="BorderBrush" Value="#015478"></Setter>
                            <Setter Property="Margin" Value="-1"></Setter>
                            <Style.Triggers>
                                <Trigger Property="IsSelected" Value="False">
                                    <Setter Property="Background" Value="#015478"/>
                                </Trigger>
                                <!-- 添加这个触发器处理窗口失去焦点时的情况 -->
                                <MultiTrigger>
                                    <MultiTrigger.Conditions>
                                        <Condition Property="IsSelected" Value="True"/>
                                        <Condition Property="Selector.IsSelectionActive" Value="False"/>
                                    </MultiTrigger.Conditions>
                                    <Setter Property="Background" Value="#027DB4"/>
                                    <Setter Property="Foreground" Value="White"/>
                                </MultiTrigger>
                            </Style.Triggers>
                        </Style>
                        <Style TargetType="{x:Type DataGridColumnHeader}">
                            <Setter Property="Background" Value="#027DB4"/>
                            <Setter Property="Foreground" Value="White"/>
                            <Setter Property="FontWeight" Value="Bold"/>
                            <Setter Property="Height" Value="34"/>
                            <Setter Property="FontSize" Value="13"/>
                            <Setter Property="HorizontalContentAlignment" Value="Center"/>
                        </Style>
                    </DataGrid.Resources>
                    <DataGrid.Columns>
                        <DataGridTemplateColumn Width="80" Header="时间">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBlock Text="{Binding dDate, StringFormat='HH:mm:ss'}" Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Center"></TextBlock>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                        <DataGridTemplateColumn Width="240" Header="总成型号">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBlock Text="{Binding ZC_Model}" Foreground="White" VerticalAlignment="Center"></TextBlock>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                        <DataGridTemplateColumn Width="194" Header="总成设备标识码">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBlock Text="{Binding ZC_SerialNo}" Foreground="White" VerticalAlignment="Center"></TextBlock>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                        <DataGridTemplateColumn Width="134" Header="激光打码编码">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBlock Text="{Binding ZC_LaserNo}" Foreground="White" VerticalAlignment="Center"></TextBlock>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                        <DataGridTemplateColumn Width="60" Header="综合结果">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBlock FontWeight="Bold" FontSize="17" HorizontalAlignment="Center" VerticalAlignment="Center">
                                        <TextBlock.Style>
                                            <Style TargetType="{x:Type TextBlock}">
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding ZC_Result}" Value="OK">
                                                        <Setter Property="Foreground" Value="#00FFFF" />
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding ZC_Result}" Value="NG">
                                                        <Setter Property="Foreground" Value="Red" />
                                                    </DataTrigger>
                                                    <!-- 其他触发器 -->
                                                </Style.Triggers>
                                            </Style>
                                        </TextBlock.Style>
                                        <Run Text="{Binding ZC_Result}" />
                                    </TextBlock>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </WrapPanel>
            <Label Content="    数据详情" Grid.Row="2" Grid.Column="2" Style="{DynamicResource GroupTitle}"></Label>
            <WrapPanel Grid.Row="3" Grid.Column="2" Background="#015478">
                <Label Content="总成" Style="{DynamicResource DataDetailTitle}" Height="60"></Label>
                <Border Style="{DynamicResource DataDetailContentBorder}" Width="1032" Height="60">
                    <Grid Height="60" Background="Transparent">
                        <Grid.RowDefinitions>
                            <RowDefinition></RowDefinition>
                            <RowDefinition></RowDefinition>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"></ColumnDefinition>
                            <ColumnDefinition Width="250"></ColumnDefinition>
                        </Grid.ColumnDefinitions>
                        <WrapPanel Grid.Row="0">
                            <Label Content="标识码：" Style="{DynamicResource DataDetailFieldTitle_5Words}" ></Label>
                            <Label Content="{Binding dataModel_Search.ZC_SerialNo}" Style="{DynamicResource DataDetailFieldValue}"></Label>
                            <Label Content="生产日期：" Style="{DynamicResource DataDetailFieldTitle_5Words}"></Label>
                            <Label Content="{Binding dataModel_Search.ZC_Time,StringFormat=yyyy-MM-dd}" Style="{DynamicResource DataDetailFieldValue}" ></Label>
                        </WrapPanel>
                        <WrapPanel Grid.Row="1">
                            <Label Content="总成型号：" Style="{DynamicResource DataDetailFieldTitle_5Words}"></Label>
                            <Label Content="{Binding dataModel_Search.ZC_Model}" Style="{DynamicResource DataDetailFieldValue}"></Label>
                            <Label Content="激光打标码：" Style="{DynamicResource DataDetailFieldTitle_5Words}"></Label>
                            <Label Content="{Binding dataModel_Search.ZC_LaserNo}" Style="{DynamicResource DataDetailFieldValue}"></Label>
                        </WrapPanel>
                        <Label Content="综合检测结果：" Style="{DynamicResource DataDetailFieldTitle_6Words}" FontWeight="Bold" Grid.Column="1" Grid.RowSpan="2" HorizontalAlignment="Left"></Label>
                        <TextBlock Grid.Column="1" Grid.RowSpan="2" Text="{Binding dataModel_Search.ZC_Result}"   FontSize="30" FontFamily="Arial" Height="50" Width="60" Margin="150,13,5,0" FontWeight="Bold">
                            <TextBlock.Style>
                                <Style TargetType="{x:Type TextBlock}">
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding dataModel_Search.ZC_Result}" Value="OK">
                                            <Setter Property="Foreground" Value="#00FFFF" />
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding dataModel_Search.ZC_Result}" Value="NG">
                                            <Setter Property="Foreground" Value="Red" />
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding dataModel_Search.ZC_Result}" Value="NA">
                                            <Setter Property="Foreground" Value="Gray" />
                                        </DataTrigger>
                                        <!-- 其他触发器 -->
                                    </Style.Triggers>
                                </Style>
                            </TextBlock.Style>
        <Run Text="{Binding dataModel_Search.ZC_Result}" />
                        </TextBlock>
                    </Grid>
                </Border>
                <Label Content="分零件：主体" Style="{DynamicResource DataDetailTitle}" Height="60"></Label>
                <Border Style="{DynamicResource DataDetailContentBorder}" Width="1032" Height="60">
                    <Grid Height="60" Background="Transparent">
                        <Grid.RowDefinitions>
                            <RowDefinition></RowDefinition>
                            <RowDefinition></RowDefinition>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"></ColumnDefinition>
                            <ColumnDefinition Width="250"></ColumnDefinition>
                        </Grid.ColumnDefinitions>
                        <WrapPanel Grid.Row="0" Grid.ColumnSpan="2">
                            <Label Content="型号码：" Style="{DynamicResource DataDetailFieldTitle_5Words}" ></Label>
                            <Label Content="{Binding dataModel_Search.FLJ_ZT_Model}" Style="{DynamicResource DataDetailFieldValue}"></Label>
                            <Label Content="物料编码：" Style="{DynamicResource DataDetailFieldTitle_5Words}"></Label>
                            <Label Content="{Binding dataModel_Search.FLJ_ZT_Material}" Style="{DynamicResource DataDetailFieldValue}" ></Label>
                        </WrapPanel>
                        <WrapPanel Grid.Row="1" Grid.ColumnSpan="2">
                            <Label Content="位移检测值：" Style="{DynamicResource DataDetailFieldTitle_5Words}"></Label>
                            <Label Content="{Binding dataModel_Search.FLJ_ZT_Value}" Style="{DynamicResource DataDetailFieldValue_Tiny}"></Label>
                            <Label Content="上限：" Style="{DynamicResource DataDetailFieldTitle_4Words}" Margin="25,0,0,0"></Label>
                            <Label Content="{Binding dataModel_Search.FLJ_ZT_Upper}" Grid.Row="1" Style="{DynamicResource DataDetailFieldValue_Tiny}"></Label>
                            <Label Content="下限：" Style="{DynamicResource DataDetailFieldTitle_4Words}" Margin="25,0,0,0"></Label>
                            <Label Content="{Binding dataModel_Search.FLJ_ZT_Lower}" Grid.Row="1" Style="{DynamicResource DataDetailFieldValue_Tiny}"></Label>
                        </WrapPanel>
                        <WrapPanel Grid.Column="1" Grid.RowSpan="2" VerticalAlignment="Center">
                            <Label Content="异物位移检测结果：" Style="{DynamicResource DataDetailFieldTitle_6Words}" FontWeight="Bold" ></Label>
                            <TextBlock FontWeight="Bold" FontSize="22" HorizontalAlignment="Center" VerticalAlignment="Center" Width="65" TextAlignment="Center">
                                <TextBlock.Style>
                                    <Style TargetType="{x:Type TextBlock}">
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding dataModel_Search.FLJ_ZT_Result}" Value="OK">
                                                <Setter Property="Foreground" Value="#00FFFF" />
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding dataModel_Search.FLJ_ZT_Result}" Value="NG">
                                                <Setter Property="Foreground" Value="Red" />
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding dataModel_Search.FLJ_ZT_Result}" Value="NA">
                                                <Setter Property="Foreground" Value="Gray" />
                                            </DataTrigger>
                                            <!-- 其他触发器 -->
                                        </Style.Triggers>
                                    </Style>
                                </TextBlock.Style>
                    <Run Text="{Binding dataModel_Search.FLJ_ZT_Result}" />
                            </TextBlock>
                        </WrapPanel>
                    </Grid>
                </Border>
                <Label Content="分零件：一次圈" Style="{DynamicResource DataDetailTitle}" Height="30"></Label>
                <Border Style="{DynamicResource DataDetailContentBorder}" Width="1032" Height="30">
                    <Grid Height="30" Background="Transparent">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="30"></RowDefinition>
                            <RowDefinition Height="0"></RowDefinition>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"></ColumnDefinition>
                            <ColumnDefinition Width="200"></ColumnDefinition>
                        </Grid.ColumnDefinitions>
                        <WrapPanel Grid.Row="0" Grid.ColumnSpan="2">
                            <Label Content="型号码：" Style="{DynamicResource DataDetailFieldTitle_5Words}" ></Label>
                            <Label Content="{Binding dataModel_Search.FLJ_YCQ_Model}" Style="{DynamicResource DataDetailFieldValue}"></Label>
                            <Label Content="物料编码：" Style="{DynamicResource DataDetailFieldTitle_5Words}"></Label>
                            <Label Content="{Binding dataModel_Search.FLJ_YCQ_Material}" Style="{DynamicResource DataDetailFieldValue}" ></Label>
                        </WrapPanel>
                        <WrapPanel Grid.Row="1" Grid.ColumnSpan="2">
                            <Label Content="位移检测值：" Style="{DynamicResource DataDetailFieldTitle_5Words}" Visibility="Collapsed"></Label>
                            <Label Content="{Binding dataModel_Search.FLJ_YCQ_Value}" Style="{DynamicResource DataDetailFieldValue_Tiny}" Visibility="Collapsed"></Label>
                            <Label Content="上限：" Style="{DynamicResource DataDetailFieldTitle_4Words}"  Margin="25,0,0,0" Visibility="Collapsed"></Label>
                            <Label Content="{Binding dataModel_Search.FLJ_YCQ_Upper}" Grid.Row="1" Style="{DynamicResource DataDetailFieldValue_Tiny}" Visibility="Collapsed"></Label>
                            <Label Content="下限：" Style="{DynamicResource DataDetailFieldTitle_4Words}" Margin="25,0,0,0" Visibility="Collapsed" ></Label>
                            <Label Content="{Binding dataModel_Search.FLJ_YCQ_Lower}" Grid.Row="1" Style="{DynamicResource DataDetailFieldValue_Tiny}" Visibility="Collapsed"></Label>
                            <Label Content="检测结果：" Style="{DynamicResource DataDetailFieldTitle_6Words}" FontWeight="Bold" Margin="204,0,0,0" Visibility="Collapsed"></Label>
                            <TextBlock FontWeight="Bold" FontSize="22" HorizontalAlignment="Center" VerticalAlignment="Center" Width="65" TextAlignment="Center" Visibility="Collapsed">
                                <TextBlock.Style>
                                    <Style TargetType="{x:Type TextBlock}">
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding dataModel_Search.FLJ_YCQ_Result}" Value="OK">
                                                <Setter Property="Foreground" Value="#00FFFF" />
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding dataModel_Search.FLJ_YCQ_Result}" Value="NG">
                                                <Setter Property="Foreground" Value="Red" />
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding dataModel_Search.FLJ_YCQ_Result}" Value="NA">
                                                <Setter Property="Foreground" Value="Gray" />
                                            </DataTrigger>
                                            <!-- 其他触发器 -->
                                        </Style.Triggers>
                                    </Style>
                                </TextBlock.Style>
                    <Run Text="{Binding dataModel_Search.FLJ_YCQ_Result}" />
                            </TextBlock>
                        </WrapPanel>
                    </Grid>
                </Border>
                <Label Content="分零件：隔离环" Style="{DynamicResource DataDetailTitle}" Height="60"></Label>
                <Border Style="{DynamicResource DataDetailContentBorder}" Width="1032" Height="60">
                    <Grid Height="60" Background="Transparent">
                        <Grid.RowDefinitions>
                            <RowDefinition></RowDefinition>
                            <RowDefinition></RowDefinition>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"></ColumnDefinition>
                            <ColumnDefinition Width="250"></ColumnDefinition>
                        </Grid.ColumnDefinitions>
                        <WrapPanel Grid.Row="0" Grid.ColumnSpan="2">
                            <Label Content="型号码：" Style="{DynamicResource DataDetailFieldTitle_5Words}" ></Label>
                            <Label Content="{Binding dataModel_Search.FLJ_GLH_Model}" Style="{DynamicResource DataDetailFieldValue}"></Label>
                            <Label Content="物料编码：" Style="{DynamicResource DataDetailFieldTitle_5Words}"></Label>
                            <Label Content="{Binding dataModel_Search.FLJ_GLH_Material}" Style="{DynamicResource DataDetailFieldValue}" ></Label>
                        </WrapPanel>
                        <WrapPanel Grid.Row="1" Grid.ColumnSpan="2">
                            <Label Content="位移检测值：" Style="{DynamicResource DataDetailFieldTitle_5Words}"></Label>
                            <Label Content="{Binding dataModel_Search.FLJ_GLH_Value}" Style="{DynamicResource DataDetailFieldValue_Tiny}"></Label>
                            <Label Content="上限：" Style="{DynamicResource DataDetailFieldTitle_4Words}"  Margin="25,0,0,0"></Label>
                            <Label Content="{Binding dataModel_Search.FLJ_GLH_Upper}" Grid.Row="1" Style="{DynamicResource DataDetailFieldValue_Tiny}"></Label>
                            <Label Content="下限：" Style="{DynamicResource DataDetailFieldTitle_4Words}"  Margin="25,0,0,0"></Label>
                            <Label Content="{Binding dataModel_Search.FLJ_GLH_Lower}" Grid.Row="1" Style="{DynamicResource DataDetailFieldValue_Tiny}"></Label>
                        </WrapPanel>
                        <WrapPanel Grid.Column="1" Grid.RowSpan="2" VerticalAlignment="Center">
                            <Label Content="检测结果：" Style="{DynamicResource DataDetailFieldTitle_6Words}" FontWeight="Bold" ></Label>
                            <TextBlock FontWeight="Bold" FontSize="22" HorizontalAlignment="Center" VerticalAlignment="Center" Width="65" TextAlignment="Center">
                                <TextBlock.Style>
                                    <Style TargetType="{x:Type TextBlock}">
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding dataModel_Search.FLJ_GLH_Result}" Value="OK">
                                                <Setter Property="Foreground" Value="#00FFFF" />
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding dataModel_Search.FLJ_GLH_Result}" Value="NG">
                                                <Setter Property="Foreground" Value="Red" />
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding dataModel_Search.FLJ_GLH_Result}" Value="NA">
                                                <Setter Property="Foreground" Value="Gray" />
                                            </DataTrigger>
                                            <!-- 其他触发器 -->
                                        </Style.Triggers>
                                    </Style>
                                </TextBlock.Style>
                    <Run Text="{Binding dataModel_Search.FLJ_GLH_Result}" />
                            </TextBlock>
                        </WrapPanel>
                    </Grid>
                </Border>
                <Label Content="分零件：二次圈" Style="{DynamicResource DataDetailTitle}" Height="60"></Label>
                <Border Style="{DynamicResource DataDetailContentBorder}" Width="1032" Height="60">
                    <Grid Height="60" Background="Transparent">
                        <Grid.RowDefinitions>
                            <RowDefinition></RowDefinition>
                            <RowDefinition></RowDefinition>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"></ColumnDefinition>
                            <ColumnDefinition Width="250"></ColumnDefinition>
                        </Grid.ColumnDefinitions>
                        <WrapPanel Grid.Row="0" Grid.ColumnSpan="2">
                            <Label Content="型号码：" Style="{DynamicResource DataDetailFieldTitle_5Words}" ></Label>
                            <Label Content="{Binding dataModel_Search.FLJ_ECQ_Model}" Style="{DynamicResource DataDetailFieldValue}"></Label>
                            <Label Content="物料编码：" Style="{DynamicResource DataDetailFieldTitle_5Words}"></Label>
                            <Label Content="{Binding dataModel_Search.FLJ_ECQ_Material}" Style="{DynamicResource DataDetailFieldValue}" ></Label>
                        </WrapPanel>
                        <WrapPanel Grid.Row="1" Grid.ColumnSpan="2">
                            <Label Content="位移检测值：" Style="{DynamicResource DataDetailFieldTitle_5Words}"></Label>
                            <Label Content="{Binding dataModel_Search.FLJ_ECQ_Value}" Style="{DynamicResource DataDetailFieldValue_Tiny}"></Label>
                            <Label Content="上限：" Style="{DynamicResource DataDetailFieldTitle_4Words}"  Margin="25,0,0,0"></Label>
                            <Label Content="{Binding dataModel_Search.FLJ_ECQ_Upper}" Grid.Row="1" Style="{DynamicResource DataDetailFieldValue_Tiny}"></Label>
                            <Label Content="下限：" Style="{DynamicResource DataDetailFieldTitle_4Words}"  Margin="25,0,0,0"></Label>
                            <Label Content="{Binding dataModel_Search.FLJ_ECQ_Lower}" Grid.Row="1" Style="{DynamicResource DataDetailFieldValue_Tiny}"></Label>
                        </WrapPanel>
                        <WrapPanel Grid.Column="1" Grid.RowSpan="2" VerticalAlignment="Center">
                            <Label Content="检测结果：" Style="{DynamicResource DataDetailFieldTitle_6Words}" FontWeight="Bold"></Label>
                            <TextBlock FontWeight="Bold" FontSize="22" HorizontalAlignment="Center" VerticalAlignment="Center" Width="65" TextAlignment="Center">
                                <TextBlock.Style>
                                    <Style TargetType="{x:Type TextBlock}">
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding dataModel_Search.FLJ_ECQ_Result}" Value="OK">
                                                <Setter Property="Foreground" Value="#00FFFF" />
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding dataModel_Search.FLJ_ECQ_Result}" Value="NG">
                                                <Setter Property="Foreground" Value="Red" />
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding dataModel_Search.FLJ_ECQ_Result}" Value="NA">
                                                <Setter Property="Foreground" Value="Gray" />
                                            </DataTrigger>
                                            <!-- 其他触发器 -->
                                        </Style.Triggers>
                                    </Style>
                                </TextBlock.Style>
                    <Run Text="{Binding dataModel_Search.FLJ_ECQ_Result}" />
                            </TextBlock>
                        </WrapPanel>
                    </Grid>
                </Border>
                <Label Content="分零件：安全环" Style="{DynamicResource DataDetailTitle}" Height="60"></Label>
                <Border Style="{DynamicResource DataDetailContentBorder}" Width="1032" Height="60">
                    <Grid Height="60" Background="Transparent">
                        <Grid.RowDefinitions>
                            <RowDefinition></RowDefinition>
                            <RowDefinition></RowDefinition>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"></ColumnDefinition>
                            <ColumnDefinition Width="250"></ColumnDefinition>
                        </Grid.ColumnDefinitions>
                        <WrapPanel Grid.Row="0" Grid.ColumnSpan="2">
                            <Label Content="型号码：" Style="{DynamicResource DataDetailFieldTitle_5Words}" ></Label>
                            <Label Content="{Binding dataModel_Search.FLJ_KZH_Model}" Style="{DynamicResource DataDetailFieldValue}"></Label>
                            <Label Content="物料编码：" Style="{DynamicResource DataDetailFieldTitle_5Words}"></Label>
                            <Label Content="{Binding dataModel_Search.FLJ_KZH_Material}" Style="{DynamicResource DataDetailFieldValue}" ></Label>
                        </WrapPanel>
                        <WrapPanel Grid.Row="1" Grid.ColumnSpan="2">
                            <Label Content="位移检测值：" Style="{DynamicResource DataDetailFieldTitle_5Words}"></Label>
                            <Label Content="{Binding dataModel_Search.FLJ_KZH_Value}" Style="{DynamicResource DataDetailFieldValue_Tiny}"></Label>
                            <Label Content="上限：" Style="{DynamicResource DataDetailFieldTitle_4Words}"  Margin="25,0,0,0"></Label>
                            <Label Content="{Binding dataModel_Search.FLJ_KZH_Upper}" Grid.Row="1" Style="{DynamicResource DataDetailFieldValue_Tiny}"></Label>
                            <Label Content="下限：" Style="{DynamicResource DataDetailFieldTitle_4Words}"  Margin="25,0,0,0"></Label>
                            <Label Content="{Binding dataModel_Search.FLJ_KZH_Lower}" Grid.Row="1" Style="{DynamicResource DataDetailFieldValue_Tiny}"></Label>
                            <Label Content="压力检测：" Style="{DynamicResource DataDetailFieldTitle_4Words}"></Label>
                            <TextBlock FontWeight="Bold" FontSize="22" HorizontalAlignment="Center" VerticalAlignment="Center" Width="65" TextAlignment="Center">
                                <TextBlock.Style>
                                    <Style TargetType="{x:Type TextBlock}">
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding dataModel_Search.FLJ_KZH_PressureResult}" Value="OK">
                                                <Setter Property="Foreground" Value="#00FFFF" />
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding dataModel_Search.FLJ_KZH_PressureResult}" Value="NG">
                                                <Setter Property="Foreground" Value="Red" />
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding dataModel_Search.FLJ_KZH_PressureResult}" Value="NA">
                                                <Setter Property="Foreground" Value="Gray" />
                                            </DataTrigger>
                                            <!-- 其他触发器 -->
                                        </Style.Triggers>
                                    </Style>
                                </TextBlock.Style>
                                <Run Text="{Binding dataModel_Search.FLJ_KZH_PressureResult}" />
                            </TextBlock>
                            <Button Content="压力曲线图" Tag="ylqxt_search"  Style="{DynamicResource FileLinkButton}" Width="85" Height="24" Click="Button_Click" ></Button>
                        </WrapPanel>
                        <WrapPanel Grid.Column="1" Grid.RowSpan="2" VerticalAlignment="Center">
                            <Label Content="检测结果：" Style="{DynamicResource DataDetailFieldTitle_6Words}" FontWeight="Bold"  ></Label>
                            <TextBlock FontWeight="Bold" FontSize="22" HorizontalAlignment="Center" VerticalAlignment="Center" Width="65" TextAlignment="Center">
                                <TextBlock.Style>
                                    <Style TargetType="{x:Type TextBlock}">
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding dataModel_Search.FLJ_KZH_Result}" Value="OK">
                                                <Setter Property="Foreground" Value="#00FFFF" />
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding dataModel_Search.FLJ_KZH_Result}" Value="NG">
                                                <Setter Property="Foreground" Value="Red" />
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding dataModel_Search.FLJ_KZH_Result}" Value="NA">
                                                <Setter Property="Foreground" Value="Gray" />
                                            </DataTrigger>
                                            <!-- 其他触发器 -->
                                        </Style.Triggers>
                                    </Style>
                                </TextBlock.Style>
                    <Run Text="{Binding dataModel_Search.FLJ_KZH_Result}" />
                            </TextBlock>
                        </WrapPanel>
                    </Grid>
                </Border>
                <Label Content="分零件：锁扣" Style="{DynamicResource DataDetailTitle}" Height="60"></Label>
                <Border Style="{DynamicResource DataDetailContentBorder}" Width="1032" Height="60">
                    <Grid Height="60" Background="Transparent">
                        <Grid.RowDefinitions>
                            <RowDefinition></RowDefinition>
                            <RowDefinition></RowDefinition>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"></ColumnDefinition>
                            <ColumnDefinition Width="250"></ColumnDefinition>
                        </Grid.ColumnDefinitions>
                        <WrapPanel Grid.Row="0" Grid.ColumnSpan="2">
                            <Label Content="型号码：" Style="{DynamicResource DataDetailFieldTitle_5Words}" ></Label>
                            <Label Content="{Binding dataModel_Search.FLJ_SK_Model}" Style="{DynamicResource DataDetailFieldValue}"></Label>
                            <Label Content="物料编码：" Style="{DynamicResource DataDetailFieldTitle_5Words}"></Label>
                            <Label Content="{Binding dataModel_Search.FLJ_SK_Material}" Style="{DynamicResource DataDetailFieldValue}" ></Label>
                        </WrapPanel>
                        <WrapPanel Grid.Row="1" Grid.ColumnSpan="2">
                            <Label Content="相机存储路径：" Style="{DynamicResource DataDetailFieldTitle_5Words}"></Label>
                            <Button Content="{Binding dataModel_Search.FLJ_SK_Photo}" Tag="path" Style="{DynamicResource FileLinkButton}" Width="666" Height="24" Click="Button_Click"></Button>
                        </WrapPanel>
                        <WrapPanel Grid.Column="1" Grid.RowSpan="2" VerticalAlignment="Center">
                            <Label Content="检测结果：" Style="{DynamicResource DataDetailFieldTitle_6Words}" FontWeight="Bold" ></Label>
                            <!--<Label Content="{Binding dataModel_Search.FLJ_SK_Result}" Grid.Row="1" Style="{DynamicResource DataDetailTestResult}"></Label>-->
                            <TextBlock FontWeight="Bold" FontSize="22" HorizontalAlignment="Center" VerticalAlignment="Center" Width="65" TextAlignment="Center">
                                <TextBlock.Style>
                                    <Style TargetType="{x:Type TextBlock}">
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding dataModel_Search.FLJ_SK_Result}" Value="OK">
                                                <Setter Property="Foreground" Value="#00FFFF" />
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding dataModel_Search.FLJ_SK_Result}" Value="NG">
                                                <Setter Property="Foreground" Value="Red" />
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding dataModel_Search.FLJ_SK_Result}" Value="NA">
                                                <Setter Property="Foreground" Value="Gray" />
                                            </DataTrigger>
                                            <!-- 其他触发器 -->
                                        </Style.Triggers>
                                    </Style>
                                </TextBlock.Style>
                    <Run Text="{Binding dataModel_Search.FLJ_SK_Result}" />
                            </TextBlock>
                        </WrapPanel>
                    </Grid>
                </Border>
                <Label Content="分零件：内密" Style="{DynamicResource DataDetailTitle}" Height="60"></Label>
                <Border Style="{DynamicResource DataDetailContentBorder}" Width="1032" Height="60">
                    <Grid Height="60" Background="Transparent">
                        <Grid.RowDefinitions>
                            <RowDefinition></RowDefinition>
                            <RowDefinition></RowDefinition>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"></ColumnDefinition>
                            <ColumnDefinition Width="190"></ColumnDefinition>
                        </Grid.ColumnDefinitions>
                        <WrapPanel Grid.Row="0" Grid.ColumnSpan="2">
                            <Label Content="相机1存储路径：" Style="{DynamicResource DataDetailFieldTitle_5Words}" ></Label>
                            <Button Content="{Binding dataModel_Search.JCY_NM_Photo1}" Tag="path" Style="{DynamicResource FileLinkButton}" Width="666" Height="24" Click="Button_Click"></Button>
                        </WrapPanel>
                        <WrapPanel Grid.Row="1" Grid.ColumnSpan="2" VerticalAlignment="Center">
                            <Label Content="相机2存储路径：" Style="{DynamicResource DataDetailFieldTitle_5Words}"></Label>
                            <Button Content="{Binding dataModel_Search.JCY_NM_Photo2}" Tag="path" Style="{DynamicResource FileLinkButton}" Width="666" Height="24" Click="Button_Click"></Button>
                        </WrapPanel>
                        <WrapPanel Grid.Column="1" Grid.RowSpan="2" VerticalAlignment="Center">
                            <Label Content="检测结果：" Style="{DynamicResource DataDetailFieldTitle_4Words}" FontWeight="Bold" ></Label>
                            <!--<Label Content="{Binding dataModel_Search.JCY_NM_Result}" Grid.Row="1" Style="{DynamicResource DataDetailTestResult}"></Label>-->
                            <TextBlock FontWeight="Bold" FontSize="22" HorizontalAlignment="Center" VerticalAlignment="Center" Width="65" TextAlignment="Center">
                                <TextBlock.Style>
                                    <Style TargetType="{x:Type TextBlock}">
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding dataModel_Search.JCY_NM_Result}" Value="OK">
                                                <Setter Property="Foreground" Value="#00FFFF" />
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding dataModel_Search.JCY_NM_Result}" Value="NG">
                                                <Setter Property="Foreground" Value="Red" />
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding dataModel_Search.JCY_NM_Result}" Value="NA">
                                                <Setter Property="Foreground" Value="Gray" />
                                            </DataTrigger>
                                            <!-- 其他触发器 -->
                                        </Style.Triggers>
                                    </Style>
                                </TextBlock.Style>
                    <Run Text="{Binding dataModel_Search.JCY_NM_Result}" />
                            </TextBlock>
                        </WrapPanel>
                    </Grid>
                </Border>
                <Label Content="分零件：外密" Style="{DynamicResource DataDetailTitle}" Height="90"></Label>
                <Border Style="{DynamicResource DataDetailContentBorder}" Width="1032" Height="90">
                    <Grid Height="90" Background="Transparent">
                        <Grid.RowDefinitions>
                            <RowDefinition></RowDefinition>
                            <RowDefinition></RowDefinition>
                            <RowDefinition></RowDefinition>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"></ColumnDefinition>
                            <ColumnDefinition Width="190"></ColumnDefinition>
                        </Grid.ColumnDefinitions>
                        <WrapPanel Grid.Row="0" Grid.ColumnSpan="2">
                            <Label Content="型号码：" Style="{DynamicResource DataDetailFieldTitle_5Words}" ></Label>
                            <Label Content="{Binding dataModel_Search.FLJ_WM_Model}" Style="{DynamicResource DataDetailFieldValue}"></Label>
                            <Label Content="物料编码：" Style="{DynamicResource DataDetailFieldTitle_5Words}"></Label>
                            <Label Content="{Binding dataModel_Search.FLJ_WM_Material}" Style="{DynamicResource DataDetailFieldValue}" ></Label>
                        </WrapPanel>
                        <WrapPanel Grid.Row="1" VerticalAlignment="Center">
                            <Label Content="相机1存储路径：" Style="{DynamicResource DataDetailFieldTitle_5Words}"></Label>
                            <Button Content="{Binding dataModel_Search.FLJ_WM_Photo1}" Tag="path" Style="{DynamicResource FileLinkButton}" Width="666" Height="24" Click="Button_Click"></Button>
                        </WrapPanel>
                        <WrapPanel Grid.Row="2" VerticalAlignment="Center">
                            <Label Content="相机2存储路径：" Style="{DynamicResource DataDetailFieldTitle_5Words}"></Label>
                            <Button Content="{Binding dataModel_Search.FLJ_WM_Photo2}" Tag="path" Style="{DynamicResource FileLinkButton}" Width="666" Height="24" Click="Button_Click"></Button>
                        </WrapPanel>
                        <WrapPanel Grid.Column="1" Grid.Row="1" VerticalAlignment="Center">
                            <Label Content="检测结果：" Style="{DynamicResource DataDetailFieldTitle_4Words}" FontWeight="Bold" ></Label>
                            <!--<Label Content="{Binding dataModel_Search.FLJ_WM_Result}" Grid.Row="1" Style="{DynamicResource DataDetailTestResult}"></Label>-->
                            <TextBlock FontWeight="Bold" FontSize="22" HorizontalAlignment="Center" VerticalAlignment="Center" Width="65" TextAlignment="Center">
                                <TextBlock.Style>
                                    <Style TargetType="{x:Type TextBlock}">
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding dataModel_Search.FLJ_WM_Result}" Value="OK">
                                                <Setter Property="Foreground" Value="#00FFFF" />
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding dataModel_Search.FLJ_WM_Result}" Value="NG">
                                                <Setter Property="Foreground" Value="Red" />
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding dataModel_Search.FLJ_WM_Result}" Value="NA">
                                                <Setter Property="Foreground" Value="Gray" />
                                            </DataTrigger>
                                            <!-- 其他触发器 -->
                                        </Style.Triggers>
                                    </Style>
                                </TextBlock.Style>
                    <Run Text="{Binding dataModel_Search.FLJ_WM_Result}" />
                            </TextBlock>
                        </WrapPanel>
                    </Grid>
                </Border>
                <Label Content="分零件：内密气密" Style="{DynamicResource DataDetailTitle}" Height="30"></Label>
                <Border Style="{DynamicResource DataDetailContentBorder}" Width="1032" Height="30">
                    <WrapPanel Grid.Row="1" Grid.ColumnSpan="2">
                        <Label Content="气密检测程序号：" Style="{DynamicResource DataDetailFieldTitle_5Words}"></Label>
                        <Label Content="{Binding dataModel_Search.JCY_QM_Program}" Style="{DynamicResource DataDetailFieldValue_Tiny}"></Label>
                        <Label Content="气密检测压力值：" Style="{DynamicResource DataDetailFieldTitle_5Words}" Margin="65,0,0,0"></Label>
                        <Label Content="{Binding dataModel_Search.JCY_QM_Pressure}" Style="{DynamicResource DataDetailFieldValue_Tiny}"></Label>
                        <Label Content="气密检测泄漏值：" Style="{DynamicResource DataDetailFieldTitle_5Words}" Margin="65,0,0,0"></Label>
                        <Label Content="{Binding dataModel_Search.JCY_QM_Leak}" Style="{DynamicResource DataDetailFieldValue_Tiny}"></Label>
                        <Label Content="气密检测结果：" Style="{DynamicResource DataDetailFieldTitle_6Words}" Margin="65,0,0,0" FontWeight="Bold"  ></Label>
                        <TextBlock FontWeight="Bold" FontSize="22" HorizontalAlignment="Center" VerticalAlignment="Center" Width="65" TextAlignment="Center">
                            <TextBlock.Style>
                                <Style TargetType="{x:Type TextBlock}">
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding dataModel_Search.JCY_QM_Result}" Value="OK">
                                            <Setter Property="Foreground" Value="#00FFFF" />
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding dataModel_Search.JCY_QM_Result}" Value="NG">
                                            <Setter Property="Foreground" Value="Red" />
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding dataModel_Search.JCY_QM_Result}" Value="NA">
                                            <Setter Property="Foreground" Value="Gray" />
                                        </DataTrigger>
                                        <!-- 其他触发器 -->
                                    </Style.Triggers>
                                </Style>
                            </TextBlock.Style>
                    <Run Text="{Binding dataModel_Search.JCY_QM_Result}" />
                        </TextBlock>
                    </WrapPanel>
                </Border>
            </WrapPanel>
            <WrapPanel Grid.Row="100" Grid.Column="1" HorizontalAlignment="Right" VerticalAlignment="Center">
                <Label Grid.Row="100" Grid.Column="1" Style="{DynamicResource DataDetailFieldTitle_4Words}" HorizontalAlignment="Right" Content="MQTT状态："></Label>
                <TextBlock Text="{Binding MQTTConnected}"   FontSize="20" FontFamily="Arial" FontWeight="Bold" Width="100">
                    <TextBlock.Style>
                        <Style TargetType="{x:Type TextBlock}">
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding MQTTConnected}" Value="连接正常">
                                    <Setter Property="Foreground" Value="#00FFFF" />
                                </DataTrigger>
                                <DataTrigger Binding="{Binding MQTTConnected}" Value="连接断开">
                                    <Setter Property="Foreground" Value="Red" />
                                </DataTrigger>
                                <!-- 其他触发器 -->
                            </Style.Triggers>
                        </Style>
                    </TextBlock.Style>
                 <Run Text="{Binding MQTTConnected}" />
                </TextBlock>
            </WrapPanel>
        </Grid>
    </Viewbox>
</UserControl>
